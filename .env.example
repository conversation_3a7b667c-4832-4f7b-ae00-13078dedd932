# KULLM Pro Environment Variables
# Copy this file to .env and fill in your actual values

# OpenAI API Configuration (Required for code switching)
OPENAI_API_KEY=your_openai_api_key_here

# Weights & Biases Configuration (Required for experiment tracking)
WANDB_API_KEY=your_wandb_api_key_here

# Hugging Face Configuration (Optional, for private models)
HF_TOKEN=your_huggingface_token_here

# Optional: Hugging Face Hub Cache Directory
# HF_HOME=/path/to/your/hf/cache

# Optional: CUDA Visible Devices (for multi-GPU setups)
# CUDA_VISIBLE_DEVICES=0,1,2,3

# Optional: Logging Configuration
# LOG_LEVEL=INFO
# LOG_FILE=./logs/kullm_pro.log

# Optional: Custom Model Cache Directory
# TRANSFORMERS_CACHE=/path/to/your/model/cache

# Optional: Disable Tokenizers Parallelism Warning
# TOKENIZERS_PARALLELISM=false

# Optional: Set PyTorch CUDA Memory Allocation
# PYTORCH_CUDA_ALLOC_CONF=max_split_size_mb:512
