# Configuration for continued pretraining (no masking)
# This config trains on all tokens like the original implementation

model:
  name: "Qwen/Qwen2.5-7B-Instruct"
  max_length: 2048
  torch_dtype: "float16"

training:
  num_train_epochs: 3
  per_device_train_batch_size: 2
  per_device_eval_batch_size: 2
  gradient_accumulation_steps: 8
  learning_rate: 0.0002
  weight_decay: 0.01
  warmup_ratio: 0.1
  lr_scheduler_type: "cosine"
  save_steps: 500
  eval_steps: 500
  logging_steps: 10
  save_total_limit: 3
  load_best_model_at_end: true
  metric_for_best_model: "eval_loss"
  greater_is_better: false
  evaluation_strategy: "steps"
  save_strategy: "steps"
  fp16: true
  gradient_checkpointing: true
  dataloader_pin_memory: false
  remove_unused_columns: false

lora:
  enabled: true
  r: 16
  alpha: 32
  dropout: 0.1
  bias: "none"
  task_type: "CAUSAL_LM"
  target_modules:
    - "q_proj"
    - "k_proj"
    - "v_proj"
    - "o_proj"
    - "gate_proj"
    - "up_proj"
    - "down_proj"

wandb:
  project: "kullm-pro-continued-pretraining"
  enabled: true
  entity: null
  tags: 
    - "continued-pretraining"
    - "think-tokens"
    - "all-tokens-trained"

dataset:
  think_token_start: "<think>"
  think_token_end: "</think>"
  instruction_tuning: false  # Disable masking - train on all tokens (continued pretraining)
