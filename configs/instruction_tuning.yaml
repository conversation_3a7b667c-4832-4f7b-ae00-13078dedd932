# Configuration for proper instruction tuning with masking
# This config enables proper instruction tuning where only assistant responses contribute to loss

model:
  name: "Qwen/Qwen2.5-7B-Instruct"
  max_length: 2048
  torch_dtype: "float16"

training:
  num_train_epochs: 3
  per_device_train_batch_size: 2
  per_device_eval_batch_size: 2
  gradient_accumulation_steps: 8
  learning_rate: 0.0002
  weight_decay: 0.01
  warmup_ratio: 0.1
  lr_scheduler_type: "cosine"
  save_steps: 500
  eval_steps: 500
  logging_steps: 10
  save_total_limit: 3
  load_best_model_at_end: true
  metric_for_best_model: "eval_loss"
  greater_is_better: false
  evaluation_strategy: "steps"
  save_strategy: "steps"
  fp16: true
  gradient_checkpointing: true
  dataloader_pin_memory: false
  remove_unused_columns: false

lora:
  enabled: true
  r: 16
  alpha: 32
  dropout: 0.1
  bias: "none"
  task_type: "CAUSAL_LM"
  target_modules:
    - "q_proj"
    - "k_proj"
    - "v_proj"
    - "o_proj"
    - "gate_proj"
    - "up_proj"
    - "down_proj"

wandb:
  project: "kullm-pro-instruction-tuning"
  enabled: true
  entity: null
  tags: 
    - "instruction-tuning"
    - "think-tokens"
    - "masked-training"

dataset:
  think_token_start: "<think>"
  think_token_end: "</think>"
  instruction_tuning: true  # Enable proper instruction tuning with system/user masking
