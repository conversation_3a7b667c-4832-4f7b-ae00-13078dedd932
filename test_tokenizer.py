#!/usr/bin/env python3
"""
Simple test script to check if the tokenizer loads correctly.
"""

from transformers import AutoTokenizer
import sys

def test_tokenizer(model_path):
    """Test if tokenizer loads without errors."""
    try:
        print(f"🔍 Testing tokenizer at: {model_path}")
        tokenizer = AutoTokenizer.from_pretrained(model_path, trust_remote_code=True)
        print(f"✅ Tokenizer loaded successfully!")
        print(f"📊 Vocabulary size: {len(tokenizer)}")
        
        # Test encoding/decoding
        test_text = "안녕하세요! Hello, how are you?"
        tokens = tokenizer.encode(test_text)
        decoded = tokenizer.decode(tokens)
        print(f"🧪 Test encoding/decoding:")
        print(f"   Input: {test_text}")
        print(f"   Tokens: {tokens[:10]}... (showing first 10)")
        print(f"   Decoded: {decoded}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error loading tokenizer: {e}")
        return False

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python test_tokenizer.py <model_path>")
        sys.exit(1)
    
    model_path = sys.argv[1]
    success = test_tokenizer(model_path)
    sys.exit(0 if success else 1)
