# KULLM Pro Dependencies
# Production-ready package versions with proper constraints for reasoning model fine-tuning

# Core ML Libraries
torch>=2.0.0,<3.0.0
transformers>=4.44.0,<4.46.0
datasets>=2.14.0,<3.0.0
accelerate>=0.34.0,<1.0.0
peft>=0.7.0,<1.0.0
bitsandbytes>=0.41.0,<1.0.0

# Experiment Tracking
wandb>=0.16.0,<1.0.0

# Data Processing
numpy>=1.24.0,<2.0.0
pandas>=2.0.0,<3.0.0
scikit-learn>=1.3.0,<2.0.0
tqdm>=4.65.0,<5.0.0

# OpenAI Integration for Code Switching
openai>=1.0.0,<2.0.0
tenacity>=8.2.0,<9.0.0
aiohttp>=3.8.0,<4.0.0

# Configuration Management
python-dotenv>=1.0.0,<2.0.0
PyYAML>=6.0.0,<7.0.0

# CLI Interface
fire>=0.5.0,<1.0.0

# Additional Utilities
typing-extensions>=4.0.0,<5.0.0

# Optional Development Tools
pytest>=7.0.0,<8.0.0
black>=23.0.0,<24.0.0
isort>=5.12.0,<6.0.0
flake8>=6.0.0,<7.0.0
