{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Instruction Tuning Data Inspection\n", "\n", "This notebook shows exactly what data goes into the model during **instruction tuning**, including:\n", "- Raw input tokens and their decoded text\n", "- Label masking (-100 for system/user, actual token IDs for assistant)\n", "- Visual representation of which tokens are masked vs trained\n", "- Detailed token-by-token analysis with decoded values\n", "\n", "**Key Focus**: Understanding how system and user prompts are masked with -100 while only assistant responses contribute to the loss."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "sys.path.append(os.path.dirname(os.path.abspath('.')))\n", "\n", "from src.fine_tune import FineTuningPipeline\n", "from transformers import AutoTokenizer\n", "import json\n", "import torch\n", "import pandas as pd\n", "from IPython.display import display, HTML\n", "import numpy as np"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load Sample Data"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loaded 3 samples\n", "\n", "First sample structure:\n", "{\n", "  \"question\": \"Let $\\\\frac{m}{n}$ , in lowest terms, be the probability that a randomly chosen positive divisor of $10^{99}$ is an integer multiple of $10^{88}$ . Find $m + n$ .\",\n", "  \"solution\": \"Okay, so 이 probability problem이 있다.\\nIt says probability that a randomly chosen positive divisor of \\\\(10^{99}\\\\) is integer multiple of \\\\(10^{88}\\\\) is \\\\(m/n\\\\), lowest terms, and m+n을 구해야 한다.\\nHmm. 이걸 break down해보자.\\n먼저, \\\\(10^{99}\\\\)의 divisors가 어떻게 생겼는지 보자.\\n10은 2×5이므로, \\\\(10^{99} = (2^{99})(5^{99...\n"]}], "source": ["# Load a few samples from the training data\n", "data_file = \"data/code_switched_GAIR_LIMO_train_817.jsonl\"\n", "\n", "samples = []\n", "with open(data_file, 'r', encoding='utf-8') as f:\n", "    for i, line in enumerate(f):\n", "        if i >= 3:  # Just load first 3 samples\n", "            break\n", "        samples.append(json.loads(line.strip()))\n", "\n", "print(f\"Loaded {len(samples)} samples\")\n", "print(\"\\nFirst sample structure:\")\n", "print(json.dumps(samples[0], indent=2, ensure_ascii=False)[:500] + \"...\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Initialize Instruction Tuning Pipeline"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-23 17:26:31,816 - src.fine_tune - INFO - Loaded configuration from configs/instruction_tuning.yaml\n", "\u001b[34m\u001b[1mwandb\u001b[0m: \u001b[33mWARNING\u001b[0m If you're specifying your api key in code, ensure this code is not shared publicly.\n", "\u001b[34m\u001b[1mwandb\u001b[0m: \u001b[33mWARNING\u001b[0m Consider setting the WANDB_API_KEY environment variable, or running `wandb login` from the command line.\n", "\u001b[34m\u001b[1mwandb\u001b[0m: Appending key for api.wandb.ai to your netrc file: /mnt/raid6/junkim100/.netrc\n", "\u001b[34m\u001b[1mwandb\u001b[0m: Currently logged in as: \u001b[33mjunkim100\u001b[0m (\u001b[33mjunkim\u001b[0m) to \u001b[32mhttps://api.wandb.ai\u001b[0m. Use \u001b[1m`wandb login --relogin`\u001b[0m to force relogin\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Instruction tuning enabled: True\n", "This means system/user prompts will be masked with -100\n"]}], "source": ["# Initialize instruction tuning pipeline\n", "instruction_pipeline = FineTuningPipeline(config_path=\"configs/instruction_tuning.yaml\")\n", "\n", "print(\"Instruction tuning enabled:\", instruction_pipeline.config[\"dataset\"].get(\"instruction_tuning\", True))\n", "print(\"This means system/user prompts will be masked with -100\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Tokenizer loaded: 151667 tokens\n", "Special tokens added: ['<think>', '</think>']\n"]}], "source": ["# Load tokenizer\n", "model_name = instruction_pipeline.config[\"model\"][\"name\"]\n", "tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True)\n", "\n", "# Add special tokens\n", "special_tokens = [\"<think>\", \"</think>\"]\n", "tokenizer.add_tokens(special_tokens)\n", "\n", "if tokenizer.pad_token is None:\n", "    tokenizer.pad_token = tokenizer.eos_token\n", "\n", "print(f\"Tokenizer loaded: {len(tokenizer)} tokens\")\n", "print(f\"Special tokens added: {special_tokens}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Format Training Data"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2025-06-23 17:26:33,677 - src.fine_tune - INFO - Formatted 3 training samples\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Formatted 3 samples\n", "\n", "Formatted sample structure:\n", "Messages:\n", "  USER: Let $\\frac{m}{n}$ , in lowest terms, be the probability that a randomly chosen positive divisor of $...\n", "  ASSISTANT: Okay, so 이 probability problem이 있다.\n", "It says probability that a randomly chosen positive divisor of \\...\n"]}], "source": ["# Format the sample data\n", "formatted_samples = instruction_pipeline.format_training_data(samples)\n", "\n", "print(f\"Formatted {len(formatted_samples)} samples\")\n", "print(\"\\nFormatted sample structure:\")\n", "sample = formatted_samples[0]\n", "print(\"Messages:\")\n", "for msg in sample[\"messages\"]:\n", "    print(f\"  {msg['role'].upper()}: {msg['content'][:100]}...\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Detailed Token Analysis with -100 Labels"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "================================================================================\n", "INSTRUCTION TUNING SAMPLE 1 INSPECTION\n", "================================================================================\n", "\n", "📋 ORIGINAL CONVERSATION:\n", "----------------------------------------\n", "USER: Let $\\frac{m}{n}$ , in lowest terms, be the probability that a randomly chosen positive divisor of $10^{99}$ is an integer multiple of $10^{88}$ . Find $m + n$ .\n", "\n", "ASSISTANT: Okay, so 이 probability problem이 있다.\n", "It says probability that a randomly chosen positive divisor of \\(10^{99}\\) is integer multiple of \\(10^{88}\\) is \\(m/n\\), lowest terms, and m+n을 구해야 한다.\n", "Hmm. 이걸 bre...\n", "\n", "\n", "💬 CHAT TEMPLATE OUTPUT:\n", "----------------------------------------\n", "Length: 2971 characters\n", "Text: <|im_start|>system\n", "You are <PERSON><PERSON>, created by <PERSON><PERSON><PERSON> Cloud. You are a helpful assistant.<|im_end|>\n", "<|im_start|>user\n", "Let $\\frac{m}{n}$ , in lowest terms, be the probability that a randomly chosen positive divisor of $10^{99}$ is an integer multiple of $10^{88}$ . Find $m + n$ .<|im_end|>\n", "<|im_start|>a...\n", "\n", "\n", "🎯 INSTRUCTION TUNING TOKENIZATION:\n", "==================================================\n", "Input tokens: 1769\n", "Masked tokens (labels = -100): 81\n", "Trained tokens (labels = token_id): 1688\n", "Training ratio: 95.4%\n"]}], "source": ["def inspect_instruction_tuning_sample(sample_idx=0):\n", "    \"\"\"Inspect a specific sample showing exactly what goes into the model.\"\"\"\n", "    \n", "    sample = formatted_samples[sample_idx]\n", "    messages = sample[\"messages\"]\n", "    \n", "    print(f\"\\n{'='*80}\")\n", "    print(f\"INSTRUCTION TUNING SAMPLE {sample_idx + 1} INSPECTION\")\n", "    print(f\"{'='*80}\")\n", "    \n", "    # Show original conversation\n", "    print(\"\\n📋 ORIGINAL CONVERSATION:\")\n", "    print(\"-\" * 40)\n", "    for msg in messages:\n", "        print(f\"{msg['role'].upper()}: {msg['content'][:200]}{'...' if len(msg['content']) > 200 else ''}\")\n", "        print()\n", "    \n", "    # Apply chat template\n", "    full_text = tokenizer.apply_chat_template(\n", "        messages, tokenize=False, add_generation_prompt=False\n", "    )\n", "    \n", "    print(\"\\n💬 CHAT TEMPLATE OUTPUT:\")\n", "    print(\"-\" * 40)\n", "    print(f\"Length: {len(full_text)} characters\")\n", "    print(f\"Text: {full_text[:300]}{'...' if len(full_text) > 300 else ''}\")\n", "    \n", "    # Get instruction tuning tokenization with masking\n", "    input_ids, labels = instruction_pipeline._tokenize_with_masking(messages, tokenizer)\n", "    \n", "    print(\"\\n\\n🎯 INSTRUCTION TUNING TOKENIZATION:\")\n", "    print(\"=\" * 50)\n", "    print(f\"Input tokens: {len(input_ids)}\")\n", "    print(f\"Masked tokens (labels = -100): {sum(1 for l in labels if l == -100)}\")\n", "    print(f\"Trained tokens (labels = token_id): {sum(1 for l in labels if l != -100)}\")\n", "    print(f\"Training ratio: {sum(1 for l in labels if l != -100) / len(labels) * 100:.1f}%\")\n", "    \n", "    return {\n", "        'messages': messages,\n", "        'full_text': full_text,\n", "        'input_ids': input_ids,\n", "        'labels': labels\n", "    }\n", "\n", "# Inspect first sample\n", "sample_data = inspect_instruction_tuning_sample(0)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Token-by-Token Analysis: Input IDs vs Labels"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🔍 DETAILED TOKEN ANALYSIS (First 100 tokens)\n", "====================================================================================================\n", "This shows exactly what goes into the model:\n", "- input_ids: The actual tokens fed to the model\n", "- labels: What the model tries to predict (-100 = masked, token_id = trained)\n", "====================================================================================================\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_2977142/227801354.py:54: FutureWarning: Styler.applymap has been deprecated. Use Styler.map instead.\n", "  styled_df = df.style.applymap(highlight_status, subset=['Status']) \\\n"]}, {"data": {"text/html": ["<style type=\"text/css\">\n", "#T_5ccfa_row0_col3, #T_5ccfa_row0_col4, #T_5ccfa_row1_col3, #T_5ccfa_row1_col4, #T_5ccfa_row2_col3, #T_5ccfa_row2_col4, #T_5ccfa_row3_col3, #T_5ccfa_row3_col4, #T_5ccfa_row4_col3, #T_5ccfa_row4_col4, #T_5ccfa_row5_col3, #T_5ccfa_row5_col4, #T_5ccfa_row6_col3, #T_5ccfa_row6_col4, #T_5ccfa_row7_col3, #T_5ccfa_row7_col4, #T_5ccfa_row8_col3, #T_5ccfa_row8_col4, #T_5ccfa_row9_col3, #T_5ccfa_row9_col4, #T_5ccfa_row10_col3, #T_5ccfa_row10_col4, #T_5ccfa_row11_col3, #T_5ccfa_row11_col4, #T_5ccfa_row12_col3, #T_5ccfa_row12_col4, #T_5ccfa_row13_col3, #T_5ccfa_row13_col4, #T_5ccfa_row14_col3, #T_5ccfa_row14_col4, #T_5ccfa_row15_col3, #T_5ccfa_row15_col4, #T_5ccfa_row16_col3, #T_5ccfa_row16_col4, #T_5ccfa_row17_col3, #T_5ccfa_row17_col4, #T_5ccfa_row18_col3, #T_5ccfa_row18_col4, #T_5ccfa_row19_col3, #T_5ccfa_row19_col4, #T_5ccfa_row20_col3, #T_5ccfa_row20_col4, #T_5ccfa_row21_col3, #T_5ccfa_row21_col4, #T_5ccfa_row22_col3, #T_5ccfa_row22_col4, #T_5ccfa_row23_col3, #T_5ccfa_row23_col4, #T_5ccfa_row24_col3, #T_5ccfa_row24_col4, #T_5ccfa_row25_col3, #T_5ccfa_row25_col4, #T_5ccfa_row26_col3, #T_5ccfa_row26_col4, #T_5ccfa_row27_col3, #T_5ccfa_row27_col4, #T_5ccfa_row28_col3, #T_5ccfa_row28_col4, #T_5ccfa_row29_col3, #T_5ccfa_row29_col4, #T_5ccfa_row30_col3, #T_5ccfa_row30_col4, #T_5ccfa_row31_col3, #T_5ccfa_row31_col4, #T_5ccfa_row32_col3, #T_5ccfa_row32_col4, #T_5ccfa_row33_col3, #T_5ccfa_row33_col4, #T_5ccfa_row34_col3, #T_5ccfa_row34_col4, #T_5ccfa_row35_col3, #T_5ccfa_row35_col4, #T_5ccfa_row36_col3, #T_5ccfa_row36_col4, #T_5ccfa_row37_col3, #T_5ccfa_row37_col4, #T_5ccfa_row38_col3, #T_5ccfa_row38_col4, #T_5ccfa_row39_col3, #T_5ccfa_row39_col4, #T_5ccfa_row40_col3, #T_5ccfa_row40_col4, #T_5ccfa_row41_col3, #T_5ccfa_row41_col4, #T_5ccfa_row42_col3, #T_5ccfa_row42_col4, #T_5ccfa_row43_col3, #T_5ccfa_row43_col4, #T_5ccfa_row44_col3, #T_5ccfa_row44_col4, #T_5ccfa_row45_col3, #T_5ccfa_row45_col4, #T_5ccfa_row46_col3, #T_5ccfa_row46_col4, #T_5ccfa_row47_col3, #T_5ccfa_row47_col4, #T_5ccfa_row48_col3, #T_5ccfa_row48_col4, #T_5ccfa_row49_col3, #T_5ccfa_row49_col4, #T_5ccfa_row50_col3, #T_5ccfa_row50_col4, #T_5ccfa_row51_col3, #T_5ccfa_row51_col4, #T_5ccfa_row52_col3, #T_5ccfa_row52_col4, #T_5ccfa_row53_col3, #T_5ccfa_row53_col4, #T_5ccfa_row54_col3, #T_5ccfa_row54_col4, #T_5ccfa_row55_col3, #T_5ccfa_row55_col4, #T_5ccfa_row56_col3, #T_5ccfa_row56_col4, #T_5ccfa_row57_col3, #T_5ccfa_row57_col4, #T_5ccfa_row58_col3, #T_5ccfa_row58_col4, #T_5ccfa_row59_col3, #T_5ccfa_row59_col4, #T_5ccfa_row60_col3, #T_5ccfa_row60_col4, #T_5ccfa_row61_col3, #T_5ccfa_row61_col4, #T_5ccfa_row62_col3, #T_5ccfa_row62_col4, #T_5ccfa_row63_col3, #T_5ccfa_row63_col4, #T_5ccfa_row64_col3, #T_5ccfa_row64_col4, #T_5ccfa_row65_col3, #T_5ccfa_row65_col4, #T_5ccfa_row66_col3, #T_5ccfa_row66_col4, #T_5ccfa_row67_col3, #T_5ccfa_row67_col4, #T_5ccfa_row68_col3, #T_5ccfa_row68_col4, #T_5ccfa_row69_col3, #T_5ccfa_row69_col4, #T_5ccfa_row70_col3, #T_5ccfa_row70_col4, #T_5ccfa_row71_col3, #T_5ccfa_row71_col4, #T_5ccfa_row72_col3, #T_5ccfa_row72_col4, #T_5ccfa_row73_col3, #T_5ccfa_row73_col4, #T_5ccfa_row74_col3, #T_5ccfa_row74_col4, #T_5ccfa_row75_col3, #T_5ccfa_row75_col4, #T_5ccfa_row76_col3, #T_5ccfa_row76_col4, #T_5ccfa_row77_col3, #T_5ccfa_row77_col4, #T_5ccfa_row78_col3, #T_5ccfa_row78_col4 {\n", "  background-color: #ffcccc;\n", "  color: #cc0000;\n", "  font-weight: bold;\n", "}\n", "#T_5ccfa_row79_col3, #T_5ccfa_row80_col3, #T_5ccfa_row81_col3, #T_5ccfa_row82_col3, #T_5ccfa_row83_col3, #T_5ccfa_row84_col3, #T_5ccfa_row85_col3, #T_5ccfa_row86_col3, #T_5ccfa_row87_col3, #T_5ccfa_row88_col3, #T_5ccfa_row89_col3, #T_5ccfa_row90_col3, #T_5ccfa_row91_col3, #T_5ccfa_row92_col3, #T_5ccfa_row93_col3, #T_5ccfa_row94_col3, #T_5ccfa_row95_col3, #T_5ccfa_row96_col3, #T_5ccfa_row97_col3, #T_5ccfa_row98_col3, #T_5ccfa_row99_col3 {\n", "  background-color: #ccffcc;\n", "  color: #006600;\n", "}\n", "#T_5ccfa_row79_col4, #T_5ccfa_row80_col4, #T_5ccfa_row81_col4, #T_5ccfa_row82_col4, #T_5ccfa_row83_col4, #T_5ccfa_row84_col4, #T_5ccfa_row85_col4, #T_5ccfa_row86_col4, #T_5ccfa_row87_col4, #T_5ccfa_row88_col4, #T_5ccfa_row89_col4, #T_5ccfa_row90_col4, #T_5ccfa_row91_col4, #T_5ccfa_row92_col4, #T_5ccfa_row93_col4, #T_5ccfa_row94_col4, #T_5ccfa_row95_col4, #T_5ccfa_row96_col4, #T_5ccfa_row97_col4, #T_5ccfa_row98_col4, #T_5ccfa_row99_col4 {\n", "  background-color: #ccffcc;\n", "  color: #006600;\n", "  font-weight: bold;\n", "}\n", "</style>\n", "<table id=\"T_5ccfa\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_5ccfa_level0_col0\" class=\"col_heading level0 col0\" >Position</th>\n", "      <th id=\"T_5ccfa_level0_col1\" class=\"col_heading level0 col1\" >Input Token ID</th>\n", "      <th id=\"T_5ccfa_level0_col2\" class=\"col_heading level0 col2\" >Decoded Text</th>\n", "      <th id=\"T_5ccfa_level0_col3\" class=\"col_heading level0 col3\" >Label</th>\n", "      <th id=\"T_5ccfa_level0_col4\" class=\"col_heading level0 col4\" >Status</th>\n", "      <th id=\"T_5ccfa_level0_col5\" class=\"col_heading level0 col5\" >Contributes to Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_5ccfa_row0_col0\" class=\"data row0 col0\" >0</td>\n", "      <td id=\"T_5ccfa_row0_col1\" class=\"data row0 col1\" >151644</td>\n", "      <td id=\"T_5ccfa_row0_col2\" class=\"data row0 col2\" >'<|im_start|>'</td>\n", "      <td id=\"T_5ccfa_row0_col3\" class=\"data row0 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row0_col4\" class=\"data row0 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row0_col5\" class=\"data row0 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "      <td id=\"T_5ccfa_row1_col0\" class=\"data row1 col0\" >1</td>\n", "      <td id=\"T_5ccfa_row1_col1\" class=\"data row1 col1\" >8948</td>\n", "      <td id=\"T_5ccfa_row1_col2\" class=\"data row1 col2\" >'system'</td>\n", "      <td id=\"T_5ccfa_row1_col3\" class=\"data row1 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row1_col4\" class=\"data row1 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row1_col5\" class=\"data row1 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "      <td id=\"T_5ccfa_row2_col0\" class=\"data row2 col0\" >2</td>\n", "      <td id=\"T_5ccfa_row2_col1\" class=\"data row2 col1\" >198</td>\n", "      <td id=\"T_5ccfa_row2_col2\" class=\"data row2 col2\" >'\\n'</td>\n", "      <td id=\"T_5ccfa_row2_col3\" class=\"data row2 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row2_col4\" class=\"data row2 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row2_col5\" class=\"data row2 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "      <td id=\"T_5ccfa_row3_col0\" class=\"data row3 col0\" >3</td>\n", "      <td id=\"T_5ccfa_row3_col1\" class=\"data row3 col1\" >2610</td>\n", "      <td id=\"T_5ccfa_row3_col2\" class=\"data row3 col2\" >'You'</td>\n", "      <td id=\"T_5ccfa_row3_col3\" class=\"data row3 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row3_col4\" class=\"data row3 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row3_col5\" class=\"data row3 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "      <td id=\"T_5ccfa_row4_col0\" class=\"data row4 col0\" >4</td>\n", "      <td id=\"T_5ccfa_row4_col1\" class=\"data row4 col1\" >525</td>\n", "      <td id=\"T_5ccfa_row4_col2\" class=\"data row4 col2\" >' are'</td>\n", "      <td id=\"T_5ccfa_row4_col3\" class=\"data row4 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row4_col4\" class=\"data row4 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row4_col5\" class=\"data row4 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row5\" class=\"row_heading level0 row5\" >5</th>\n", "      <td id=\"T_5ccfa_row5_col0\" class=\"data row5 col0\" >5</td>\n", "      <td id=\"T_5ccfa_row5_col1\" class=\"data row5 col1\" >1207</td>\n", "      <td id=\"T_5ccfa_row5_col2\" class=\"data row5 col2\" >' Q'</td>\n", "      <td id=\"T_5ccfa_row5_col3\" class=\"data row5 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row5_col4\" class=\"data row5 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row5_col5\" class=\"data row5 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row6\" class=\"row_heading level0 row6\" >6</th>\n", "      <td id=\"T_5ccfa_row6_col0\" class=\"data row6 col0\" >6</td>\n", "      <td id=\"T_5ccfa_row6_col1\" class=\"data row6 col1\" >16948</td>\n", "      <td id=\"T_5ccfa_row6_col2\" class=\"data row6 col2\" >'wen'</td>\n", "      <td id=\"T_5ccfa_row6_col3\" class=\"data row6 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row6_col4\" class=\"data row6 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row6_col5\" class=\"data row6 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row7\" class=\"row_heading level0 row7\" >7</th>\n", "      <td id=\"T_5ccfa_row7_col0\" class=\"data row7 col0\" >7</td>\n", "      <td id=\"T_5ccfa_row7_col1\" class=\"data row7 col1\" >11</td>\n", "      <td id=\"T_5ccfa_row7_col2\" class=\"data row7 col2\" >','</td>\n", "      <td id=\"T_5ccfa_row7_col3\" class=\"data row7 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row7_col4\" class=\"data row7 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row7_col5\" class=\"data row7 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row8\" class=\"row_heading level0 row8\" >8</th>\n", "      <td id=\"T_5ccfa_row8_col0\" class=\"data row8 col0\" >8</td>\n", "      <td id=\"T_5ccfa_row8_col1\" class=\"data row8 col1\" >3465</td>\n", "      <td id=\"T_5ccfa_row8_col2\" class=\"data row8 col2\" >' created'</td>\n", "      <td id=\"T_5ccfa_row8_col3\" class=\"data row8 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row8_col4\" class=\"data row8 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row8_col5\" class=\"data row8 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row9\" class=\"row_heading level0 row9\" >9</th>\n", "      <td id=\"T_5ccfa_row9_col0\" class=\"data row9 col0\" >9</td>\n", "      <td id=\"T_5ccfa_row9_col1\" class=\"data row9 col1\" >553</td>\n", "      <td id=\"T_5ccfa_row9_col2\" class=\"data row9 col2\" >' by'</td>\n", "      <td id=\"T_5ccfa_row9_col3\" class=\"data row9 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row9_col4\" class=\"data row9 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row9_col5\" class=\"data row9 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row10\" class=\"row_heading level0 row10\" >10</th>\n", "      <td id=\"T_5ccfa_row10_col0\" class=\"data row10 col0\" >10</td>\n", "      <td id=\"T_5ccfa_row10_col1\" class=\"data row10 col1\" >54364</td>\n", "      <td id=\"T_5ccfa_row10_col2\" class=\"data row10 col2\" >' Alibaba'</td>\n", "      <td id=\"T_5ccfa_row10_col3\" class=\"data row10 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row10_col4\" class=\"data row10 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row10_col5\" class=\"data row10 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row11\" class=\"row_heading level0 row11\" >11</th>\n", "      <td id=\"T_5ccfa_row11_col0\" class=\"data row11 col0\" >11</td>\n", "      <td id=\"T_5ccfa_row11_col1\" class=\"data row11 col1\" >14817</td>\n", "      <td id=\"T_5ccfa_row11_col2\" class=\"data row11 col2\" >' Cloud'</td>\n", "      <td id=\"T_5ccfa_row11_col3\" class=\"data row11 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row11_col4\" class=\"data row11 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row11_col5\" class=\"data row11 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row12\" class=\"row_heading level0 row12\" >12</th>\n", "      <td id=\"T_5ccfa_row12_col0\" class=\"data row12 col0\" >12</td>\n", "      <td id=\"T_5ccfa_row12_col1\" class=\"data row12 col1\" >13</td>\n", "      <td id=\"T_5ccfa_row12_col2\" class=\"data row12 col2\" >'.'</td>\n", "      <td id=\"T_5ccfa_row12_col3\" class=\"data row12 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row12_col4\" class=\"data row12 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row12_col5\" class=\"data row12 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row13\" class=\"row_heading level0 row13\" >13</th>\n", "      <td id=\"T_5ccfa_row13_col0\" class=\"data row13 col0\" >13</td>\n", "      <td id=\"T_5ccfa_row13_col1\" class=\"data row13 col1\" >1446</td>\n", "      <td id=\"T_5ccfa_row13_col2\" class=\"data row13 col2\" >' You'</td>\n", "      <td id=\"T_5ccfa_row13_col3\" class=\"data row13 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row13_col4\" class=\"data row13 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row13_col5\" class=\"data row13 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row14\" class=\"row_heading level0 row14\" >14</th>\n", "      <td id=\"T_5ccfa_row14_col0\" class=\"data row14 col0\" >14</td>\n", "      <td id=\"T_5ccfa_row14_col1\" class=\"data row14 col1\" >525</td>\n", "      <td id=\"T_5ccfa_row14_col2\" class=\"data row14 col2\" >' are'</td>\n", "      <td id=\"T_5ccfa_row14_col3\" class=\"data row14 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row14_col4\" class=\"data row14 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row14_col5\" class=\"data row14 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row15\" class=\"row_heading level0 row15\" >15</th>\n", "      <td id=\"T_5ccfa_row15_col0\" class=\"data row15 col0\" >15</td>\n", "      <td id=\"T_5ccfa_row15_col1\" class=\"data row15 col1\" >264</td>\n", "      <td id=\"T_5ccfa_row15_col2\" class=\"data row15 col2\" >' a'</td>\n", "      <td id=\"T_5ccfa_row15_col3\" class=\"data row15 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row15_col4\" class=\"data row15 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row15_col5\" class=\"data row15 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row16\" class=\"row_heading level0 row16\" >16</th>\n", "      <td id=\"T_5ccfa_row16_col0\" class=\"data row16 col0\" >16</td>\n", "      <td id=\"T_5ccfa_row16_col1\" class=\"data row16 col1\" >10950</td>\n", "      <td id=\"T_5ccfa_row16_col2\" class=\"data row16 col2\" >' helpful'</td>\n", "      <td id=\"T_5ccfa_row16_col3\" class=\"data row16 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row16_col4\" class=\"data row16 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row16_col5\" class=\"data row16 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row17\" class=\"row_heading level0 row17\" >17</th>\n", "      <td id=\"T_5ccfa_row17_col0\" class=\"data row17 col0\" >17</td>\n", "      <td id=\"T_5ccfa_row17_col1\" class=\"data row17 col1\" >17847</td>\n", "      <td id=\"T_5ccfa_row17_col2\" class=\"data row17 col2\" >' assistant'</td>\n", "      <td id=\"T_5ccfa_row17_col3\" class=\"data row17 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row17_col4\" class=\"data row17 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row17_col5\" class=\"data row17 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row18\" class=\"row_heading level0 row18\" >18</th>\n", "      <td id=\"T_5ccfa_row18_col0\" class=\"data row18 col0\" >18</td>\n", "      <td id=\"T_5ccfa_row18_col1\" class=\"data row18 col1\" >13</td>\n", "      <td id=\"T_5ccfa_row18_col2\" class=\"data row18 col2\" >'.'</td>\n", "      <td id=\"T_5ccfa_row18_col3\" class=\"data row18 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row18_col4\" class=\"data row18 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row18_col5\" class=\"data row18 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row19\" class=\"row_heading level0 row19\" >19</th>\n", "      <td id=\"T_5ccfa_row19_col0\" class=\"data row19 col0\" >19</td>\n", "      <td id=\"T_5ccfa_row19_col1\" class=\"data row19 col1\" >151645</td>\n", "      <td id=\"T_5ccfa_row19_col2\" class=\"data row19 col2\" >'<|im_end|>'</td>\n", "      <td id=\"T_5ccfa_row19_col3\" class=\"data row19 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row19_col4\" class=\"data row19 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row19_col5\" class=\"data row19 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row20\" class=\"row_heading level0 row20\" >20</th>\n", "      <td id=\"T_5ccfa_row20_col0\" class=\"data row20 col0\" >20</td>\n", "      <td id=\"T_5ccfa_row20_col1\" class=\"data row20 col1\" >198</td>\n", "      <td id=\"T_5ccfa_row20_col2\" class=\"data row20 col2\" >'\\n'</td>\n", "      <td id=\"T_5ccfa_row20_col3\" class=\"data row20 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row20_col4\" class=\"data row20 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row20_col5\" class=\"data row20 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row21\" class=\"row_heading level0 row21\" >21</th>\n", "      <td id=\"T_5ccfa_row21_col0\" class=\"data row21 col0\" >21</td>\n", "      <td id=\"T_5ccfa_row21_col1\" class=\"data row21 col1\" >151644</td>\n", "      <td id=\"T_5ccfa_row21_col2\" class=\"data row21 col2\" >'<|im_start|>'</td>\n", "      <td id=\"T_5ccfa_row21_col3\" class=\"data row21 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row21_col4\" class=\"data row21 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row21_col5\" class=\"data row21 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row22\" class=\"row_heading level0 row22\" >22</th>\n", "      <td id=\"T_5ccfa_row22_col0\" class=\"data row22 col0\" >22</td>\n", "      <td id=\"T_5ccfa_row22_col1\" class=\"data row22 col1\" >872</td>\n", "      <td id=\"T_5ccfa_row22_col2\" class=\"data row22 col2\" >'user'</td>\n", "      <td id=\"T_5ccfa_row22_col3\" class=\"data row22 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row22_col4\" class=\"data row22 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row22_col5\" class=\"data row22 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row23\" class=\"row_heading level0 row23\" >23</th>\n", "      <td id=\"T_5ccfa_row23_col0\" class=\"data row23 col0\" >23</td>\n", "      <td id=\"T_5ccfa_row23_col1\" class=\"data row23 col1\" >198</td>\n", "      <td id=\"T_5ccfa_row23_col2\" class=\"data row23 col2\" >'\\n'</td>\n", "      <td id=\"T_5ccfa_row23_col3\" class=\"data row23 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row23_col4\" class=\"data row23 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row23_col5\" class=\"data row23 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row24\" class=\"row_heading level0 row24\" >24</th>\n", "      <td id=\"T_5ccfa_row24_col0\" class=\"data row24 col0\" >24</td>\n", "      <td id=\"T_5ccfa_row24_col1\" class=\"data row24 col1\" >10061</td>\n", "      <td id=\"T_5ccfa_row24_col2\" class=\"data row24 col2\" >'Let'</td>\n", "      <td id=\"T_5ccfa_row24_col3\" class=\"data row24 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row24_col4\" class=\"data row24 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row24_col5\" class=\"data row24 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row25\" class=\"row_heading level0 row25\" >25</th>\n", "      <td id=\"T_5ccfa_row25_col0\" class=\"data row25 col0\" >25</td>\n", "      <td id=\"T_5ccfa_row25_col1\" class=\"data row25 col1\" >57960</td>\n", "      <td id=\"T_5ccfa_row25_col2\" class=\"data row25 col2\" >' $\\\\'</td>\n", "      <td id=\"T_5ccfa_row25_col3\" class=\"data row25 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row25_col4\" class=\"data row25 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row25_col5\" class=\"data row25 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row26\" class=\"row_heading level0 row26\" >26</th>\n", "      <td id=\"T_5ccfa_row26_col0\" class=\"data row26 col0\" >26</td>\n", "      <td id=\"T_5ccfa_row26_col1\" class=\"data row26 col1\" >37018</td>\n", "      <td id=\"T_5ccfa_row26_col2\" class=\"data row26 col2\" >'frac'</td>\n", "      <td id=\"T_5ccfa_row26_col3\" class=\"data row26 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row26_col4\" class=\"data row26 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row26_col5\" class=\"data row26 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row27\" class=\"row_heading level0 row27\" >27</th>\n", "      <td id=\"T_5ccfa_row27_col0\" class=\"data row27 col0\" >27</td>\n", "      <td id=\"T_5ccfa_row27_col1\" class=\"data row27 col1\" >90</td>\n", "      <td id=\"T_5ccfa_row27_col2\" class=\"data row27 col2\" >'{'</td>\n", "      <td id=\"T_5ccfa_row27_col3\" class=\"data row27 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row27_col4\" class=\"data row27 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row27_col5\" class=\"data row27 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row28\" class=\"row_heading level0 row28\" >28</th>\n", "      <td id=\"T_5ccfa_row28_col0\" class=\"data row28 col0\" >28</td>\n", "      <td id=\"T_5ccfa_row28_col1\" class=\"data row28 col1\" >76</td>\n", "      <td id=\"T_5ccfa_row28_col2\" class=\"data row28 col2\" >'m'</td>\n", "      <td id=\"T_5ccfa_row28_col3\" class=\"data row28 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row28_col4\" class=\"data row28 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row28_col5\" class=\"data row28 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row29\" class=\"row_heading level0 row29\" >29</th>\n", "      <td id=\"T_5ccfa_row29_col0\" class=\"data row29 col0\" >29</td>\n", "      <td id=\"T_5ccfa_row29_col1\" class=\"data row29 col1\" >15170</td>\n", "      <td id=\"T_5ccfa_row29_col2\" class=\"data row29 col2\" >'}{'</td>\n", "      <td id=\"T_5ccfa_row29_col3\" class=\"data row29 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row29_col4\" class=\"data row29 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row29_col5\" class=\"data row29 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row30\" class=\"row_heading level0 row30\" >30</th>\n", "      <td id=\"T_5ccfa_row30_col0\" class=\"data row30 col0\" >30</td>\n", "      <td id=\"T_5ccfa_row30_col1\" class=\"data row30 col1\" >77</td>\n", "      <td id=\"T_5ccfa_row30_col2\" class=\"data row30 col2\" >'n'</td>\n", "      <td id=\"T_5ccfa_row30_col3\" class=\"data row30 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row30_col4\" class=\"data row30 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row30_col5\" class=\"data row30 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row31\" class=\"row_heading level0 row31\" >31</th>\n", "      <td id=\"T_5ccfa_row31_col0\" class=\"data row31 col0\" >31</td>\n", "      <td id=\"T_5ccfa_row31_col1\" class=\"data row31 col1\" >31716</td>\n", "      <td id=\"T_5ccfa_row31_col2\" class=\"data row31 col2\" >'}$'</td>\n", "      <td id=\"T_5ccfa_row31_col3\" class=\"data row31 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row31_col4\" class=\"data row31 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row31_col5\" class=\"data row31 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row32\" class=\"row_heading level0 row32\" >32</th>\n", "      <td id=\"T_5ccfa_row32_col0\" class=\"data row32 col0\" >32</td>\n", "      <td id=\"T_5ccfa_row32_col1\" class=\"data row32 col1\" >1154</td>\n", "      <td id=\"T_5ccfa_row32_col2\" class=\"data row32 col2\" >' ,'</td>\n", "      <td id=\"T_5ccfa_row32_col3\" class=\"data row32 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row32_col4\" class=\"data row32 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row32_col5\" class=\"data row32 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row33\" class=\"row_heading level0 row33\" >33</th>\n", "      <td id=\"T_5ccfa_row33_col0\" class=\"data row33 col0\" >33</td>\n", "      <td id=\"T_5ccfa_row33_col1\" class=\"data row33 col1\" >304</td>\n", "      <td id=\"T_5ccfa_row33_col2\" class=\"data row33 col2\" >' in'</td>\n", "      <td id=\"T_5ccfa_row33_col3\" class=\"data row33 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row33_col4\" class=\"data row33 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row33_col5\" class=\"data row33 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row34\" class=\"row_heading level0 row34\" >34</th>\n", "      <td id=\"T_5ccfa_row34_col0\" class=\"data row34 col0\" >34</td>\n", "      <td id=\"T_5ccfa_row34_col1\" class=\"data row34 col1\" >15457</td>\n", "      <td id=\"T_5ccfa_row34_col2\" class=\"data row34 col2\" >' lowest'</td>\n", "      <td id=\"T_5ccfa_row34_col3\" class=\"data row34 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row34_col4\" class=\"data row34 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row34_col5\" class=\"data row34 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row35\" class=\"row_heading level0 row35\" >35</th>\n", "      <td id=\"T_5ccfa_row35_col0\" class=\"data row35 col0\" >35</td>\n", "      <td id=\"T_5ccfa_row35_col1\" class=\"data row35 col1\" >3793</td>\n", "      <td id=\"T_5ccfa_row35_col2\" class=\"data row35 col2\" >' terms'</td>\n", "      <td id=\"T_5ccfa_row35_col3\" class=\"data row35 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row35_col4\" class=\"data row35 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row35_col5\" class=\"data row35 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row36\" class=\"row_heading level0 row36\" >36</th>\n", "      <td id=\"T_5ccfa_row36_col0\" class=\"data row36 col0\" >36</td>\n", "      <td id=\"T_5ccfa_row36_col1\" class=\"data row36 col1\" >11</td>\n", "      <td id=\"T_5ccfa_row36_col2\" class=\"data row36 col2\" >','</td>\n", "      <td id=\"T_5ccfa_row36_col3\" class=\"data row36 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row36_col4\" class=\"data row36 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row36_col5\" class=\"data row36 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row37\" class=\"row_heading level0 row37\" >37</th>\n", "      <td id=\"T_5ccfa_row37_col0\" class=\"data row37 col0\" >37</td>\n", "      <td id=\"T_5ccfa_row37_col1\" class=\"data row37 col1\" >387</td>\n", "      <td id=\"T_5ccfa_row37_col2\" class=\"data row37 col2\" >' be'</td>\n", "      <td id=\"T_5ccfa_row37_col3\" class=\"data row37 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row37_col4\" class=\"data row37 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row37_col5\" class=\"data row37 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row38\" class=\"row_heading level0 row38\" >38</th>\n", "      <td id=\"T_5ccfa_row38_col0\" class=\"data row38 col0\" >38</td>\n", "      <td id=\"T_5ccfa_row38_col1\" class=\"data row38 col1\" >279</td>\n", "      <td id=\"T_5ccfa_row38_col2\" class=\"data row38 col2\" >' the'</td>\n", "      <td id=\"T_5ccfa_row38_col3\" class=\"data row38 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row38_col4\" class=\"data row38 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row38_col5\" class=\"data row38 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row39\" class=\"row_heading level0 row39\" >39</th>\n", "      <td id=\"T_5ccfa_row39_col0\" class=\"data row39 col0\" >39</td>\n", "      <td id=\"T_5ccfa_row39_col1\" class=\"data row39 col1\" >18927</td>\n", "      <td id=\"T_5ccfa_row39_col2\" class=\"data row39 col2\" >' probability'</td>\n", "      <td id=\"T_5ccfa_row39_col3\" class=\"data row39 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row39_col4\" class=\"data row39 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row39_col5\" class=\"data row39 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row40\" class=\"row_heading level0 row40\" >40</th>\n", "      <td id=\"T_5ccfa_row40_col0\" class=\"data row40 col0\" >40</td>\n", "      <td id=\"T_5ccfa_row40_col1\" class=\"data row40 col1\" >429</td>\n", "      <td id=\"T_5ccfa_row40_col2\" class=\"data row40 col2\" >' that'</td>\n", "      <td id=\"T_5ccfa_row40_col3\" class=\"data row40 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row40_col4\" class=\"data row40 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row40_col5\" class=\"data row40 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row41\" class=\"row_heading level0 row41\" >41</th>\n", "      <td id=\"T_5ccfa_row41_col0\" class=\"data row41 col0\" >41</td>\n", "      <td id=\"T_5ccfa_row41_col1\" class=\"data row41 col1\" >264</td>\n", "      <td id=\"T_5ccfa_row41_col2\" class=\"data row41 col2\" >' a'</td>\n", "      <td id=\"T_5ccfa_row41_col3\" class=\"data row41 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row41_col4\" class=\"data row41 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row41_col5\" class=\"data row41 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row42\" class=\"row_heading level0 row42\" >42</th>\n", "      <td id=\"T_5ccfa_row42_col0\" class=\"data row42 col0\" >42</td>\n", "      <td id=\"T_5ccfa_row42_col1\" class=\"data row42 col1\" >26618</td>\n", "      <td id=\"T_5ccfa_row42_col2\" class=\"data row42 col2\" >' randomly'</td>\n", "      <td id=\"T_5ccfa_row42_col3\" class=\"data row42 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row42_col4\" class=\"data row42 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row42_col5\" class=\"data row42 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row43\" class=\"row_heading level0 row43\" >43</th>\n", "      <td id=\"T_5ccfa_row43_col0\" class=\"data row43 col0\" >43</td>\n", "      <td id=\"T_5ccfa_row43_col1\" class=\"data row43 col1\" >11882</td>\n", "      <td id=\"T_5ccfa_row43_col2\" class=\"data row43 col2\" >' chosen'</td>\n", "      <td id=\"T_5ccfa_row43_col3\" class=\"data row43 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row43_col4\" class=\"data row43 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row43_col5\" class=\"data row43 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row44\" class=\"row_heading level0 row44\" >44</th>\n", "      <td id=\"T_5ccfa_row44_col0\" class=\"data row44 col0\" >44</td>\n", "      <td id=\"T_5ccfa_row44_col1\" class=\"data row44 col1\" >6785</td>\n", "      <td id=\"T_5ccfa_row44_col2\" class=\"data row44 col2\" >' positive'</td>\n", "      <td id=\"T_5ccfa_row44_col3\" class=\"data row44 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row44_col4\" class=\"data row44 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row44_col5\" class=\"data row44 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row45\" class=\"row_heading level0 row45\" >45</th>\n", "      <td id=\"T_5ccfa_row45_col0\" class=\"data row45 col0\" >45</td>\n", "      <td id=\"T_5ccfa_row45_col1\" class=\"data row45 col1\" >49109</td>\n", "      <td id=\"T_5ccfa_row45_col2\" class=\"data row45 col2\" >' divisor'</td>\n", "      <td id=\"T_5ccfa_row45_col3\" class=\"data row45 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row45_col4\" class=\"data row45 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row45_col5\" class=\"data row45 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row46\" class=\"row_heading level0 row46\" >46</th>\n", "      <td id=\"T_5ccfa_row46_col0\" class=\"data row46 col0\" >46</td>\n", "      <td id=\"T_5ccfa_row46_col1\" class=\"data row46 col1\" >315</td>\n", "      <td id=\"T_5ccfa_row46_col2\" class=\"data row46 col2\" >' of'</td>\n", "      <td id=\"T_5ccfa_row46_col3\" class=\"data row46 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row46_col4\" class=\"data row46 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row46_col5\" class=\"data row46 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row47\" class=\"row_heading level0 row47\" >47</th>\n", "      <td id=\"T_5ccfa_row47_col0\" class=\"data row47 col0\" >47</td>\n", "      <td id=\"T_5ccfa_row47_col1\" class=\"data row47 col1\" >400</td>\n", "      <td id=\"T_5ccfa_row47_col2\" class=\"data row47 col2\" >' $'</td>\n", "      <td id=\"T_5ccfa_row47_col3\" class=\"data row47 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row47_col4\" class=\"data row47 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row47_col5\" class=\"data row47 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row48\" class=\"row_heading level0 row48\" >48</th>\n", "      <td id=\"T_5ccfa_row48_col0\" class=\"data row48 col0\" >48</td>\n", "      <td id=\"T_5ccfa_row48_col1\" class=\"data row48 col1\" >16</td>\n", "      <td id=\"T_5ccfa_row48_col2\" class=\"data row48 col2\" >'1'</td>\n", "      <td id=\"T_5ccfa_row48_col3\" class=\"data row48 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row48_col4\" class=\"data row48 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row48_col5\" class=\"data row48 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row49\" class=\"row_heading level0 row49\" >49</th>\n", "      <td id=\"T_5ccfa_row49_col0\" class=\"data row49 col0\" >49</td>\n", "      <td id=\"T_5ccfa_row49_col1\" class=\"data row49 col1\" >15</td>\n", "      <td id=\"T_5ccfa_row49_col2\" class=\"data row49 col2\" >'0'</td>\n", "      <td id=\"T_5ccfa_row49_col3\" class=\"data row49 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row49_col4\" class=\"data row49 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row49_col5\" class=\"data row49 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row50\" class=\"row_heading level0 row50\" >50</th>\n", "      <td id=\"T_5ccfa_row50_col0\" class=\"data row50 col0\" >50</td>\n", "      <td id=\"T_5ccfa_row50_col1\" class=\"data row50 col1\" >47822</td>\n", "      <td id=\"T_5ccfa_row50_col2\" class=\"data row50 col2\" >'^{'</td>\n", "      <td id=\"T_5ccfa_row50_col3\" class=\"data row50 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row50_col4\" class=\"data row50 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row50_col5\" class=\"data row50 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row51\" class=\"row_heading level0 row51\" >51</th>\n", "      <td id=\"T_5ccfa_row51_col0\" class=\"data row51 col0\" >51</td>\n", "      <td id=\"T_5ccfa_row51_col1\" class=\"data row51 col1\" >24</td>\n", "      <td id=\"T_5ccfa_row51_col2\" class=\"data row51 col2\" >'9'</td>\n", "      <td id=\"T_5ccfa_row51_col3\" class=\"data row51 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row51_col4\" class=\"data row51 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row51_col5\" class=\"data row51 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row52\" class=\"row_heading level0 row52\" >52</th>\n", "      <td id=\"T_5ccfa_row52_col0\" class=\"data row52 col0\" >52</td>\n", "      <td id=\"T_5ccfa_row52_col1\" class=\"data row52 col1\" >24</td>\n", "      <td id=\"T_5ccfa_row52_col2\" class=\"data row52 col2\" >'9'</td>\n", "      <td id=\"T_5ccfa_row52_col3\" class=\"data row52 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row52_col4\" class=\"data row52 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row52_col5\" class=\"data row52 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row53\" class=\"row_heading level0 row53\" >53</th>\n", "      <td id=\"T_5ccfa_row53_col0\" class=\"data row53 col0\" >53</td>\n", "      <td id=\"T_5ccfa_row53_col1\" class=\"data row53 col1\" >31716</td>\n", "      <td id=\"T_5ccfa_row53_col2\" class=\"data row53 col2\" >'}$'</td>\n", "      <td id=\"T_5ccfa_row53_col3\" class=\"data row53 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row53_col4\" class=\"data row53 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row53_col5\" class=\"data row53 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row54\" class=\"row_heading level0 row54\" >54</th>\n", "      <td id=\"T_5ccfa_row54_col0\" class=\"data row54 col0\" >54</td>\n", "      <td id=\"T_5ccfa_row54_col1\" class=\"data row54 col1\" >374</td>\n", "      <td id=\"T_5ccfa_row54_col2\" class=\"data row54 col2\" >' is'</td>\n", "      <td id=\"T_5ccfa_row54_col3\" class=\"data row54 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row54_col4\" class=\"data row54 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row54_col5\" class=\"data row54 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row55\" class=\"row_heading level0 row55\" >55</th>\n", "      <td id=\"T_5ccfa_row55_col0\" class=\"data row55 col0\" >55</td>\n", "      <td id=\"T_5ccfa_row55_col1\" class=\"data row55 col1\" >458</td>\n", "      <td id=\"T_5ccfa_row55_col2\" class=\"data row55 col2\" >' an'</td>\n", "      <td id=\"T_5ccfa_row55_col3\" class=\"data row55 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row55_col4\" class=\"data row55 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row55_col5\" class=\"data row55 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row56\" class=\"row_heading level0 row56\" >56</th>\n", "      <td id=\"T_5ccfa_row56_col0\" class=\"data row56 col0\" >56</td>\n", "      <td id=\"T_5ccfa_row56_col1\" class=\"data row56 col1\" >7546</td>\n", "      <td id=\"T_5ccfa_row56_col2\" class=\"data row56 col2\" >' integer'</td>\n", "      <td id=\"T_5ccfa_row56_col3\" class=\"data row56 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row56_col4\" class=\"data row56 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row56_col5\" class=\"data row56 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row57\" class=\"row_heading level0 row57\" >57</th>\n", "      <td id=\"T_5ccfa_row57_col0\" class=\"data row57 col0\" >57</td>\n", "      <td id=\"T_5ccfa_row57_col1\" class=\"data row57 col1\" >5248</td>\n", "      <td id=\"T_5ccfa_row57_col2\" class=\"data row57 col2\" >' multiple'</td>\n", "      <td id=\"T_5ccfa_row57_col3\" class=\"data row57 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row57_col4\" class=\"data row57 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row57_col5\" class=\"data row57 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row58\" class=\"row_heading level0 row58\" >58</th>\n", "      <td id=\"T_5ccfa_row58_col0\" class=\"data row58 col0\" >58</td>\n", "      <td id=\"T_5ccfa_row58_col1\" class=\"data row58 col1\" >315</td>\n", "      <td id=\"T_5ccfa_row58_col2\" class=\"data row58 col2\" >' of'</td>\n", "      <td id=\"T_5ccfa_row58_col3\" class=\"data row58 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row58_col4\" class=\"data row58 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row58_col5\" class=\"data row58 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row59\" class=\"row_heading level0 row59\" >59</th>\n", "      <td id=\"T_5ccfa_row59_col0\" class=\"data row59 col0\" >59</td>\n", "      <td id=\"T_5ccfa_row59_col1\" class=\"data row59 col1\" >400</td>\n", "      <td id=\"T_5ccfa_row59_col2\" class=\"data row59 col2\" >' $'</td>\n", "      <td id=\"T_5ccfa_row59_col3\" class=\"data row59 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row59_col4\" class=\"data row59 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row59_col5\" class=\"data row59 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row60\" class=\"row_heading level0 row60\" >60</th>\n", "      <td id=\"T_5ccfa_row60_col0\" class=\"data row60 col0\" >60</td>\n", "      <td id=\"T_5ccfa_row60_col1\" class=\"data row60 col1\" >16</td>\n", "      <td id=\"T_5ccfa_row60_col2\" class=\"data row60 col2\" >'1'</td>\n", "      <td id=\"T_5ccfa_row60_col3\" class=\"data row60 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row60_col4\" class=\"data row60 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row60_col5\" class=\"data row60 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row61\" class=\"row_heading level0 row61\" >61</th>\n", "      <td id=\"T_5ccfa_row61_col0\" class=\"data row61 col0\" >61</td>\n", "      <td id=\"T_5ccfa_row61_col1\" class=\"data row61 col1\" >15</td>\n", "      <td id=\"T_5ccfa_row61_col2\" class=\"data row61 col2\" >'0'</td>\n", "      <td id=\"T_5ccfa_row61_col3\" class=\"data row61 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row61_col4\" class=\"data row61 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row61_col5\" class=\"data row61 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row62\" class=\"row_heading level0 row62\" >62</th>\n", "      <td id=\"T_5ccfa_row62_col0\" class=\"data row62 col0\" >62</td>\n", "      <td id=\"T_5ccfa_row62_col1\" class=\"data row62 col1\" >47822</td>\n", "      <td id=\"T_5ccfa_row62_col2\" class=\"data row62 col2\" >'^{'</td>\n", "      <td id=\"T_5ccfa_row62_col3\" class=\"data row62 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row62_col4\" class=\"data row62 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row62_col5\" class=\"data row62 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row63\" class=\"row_heading level0 row63\" >63</th>\n", "      <td id=\"T_5ccfa_row63_col0\" class=\"data row63 col0\" >63</td>\n", "      <td id=\"T_5ccfa_row63_col1\" class=\"data row63 col1\" >23</td>\n", "      <td id=\"T_5ccfa_row63_col2\" class=\"data row63 col2\" >'8'</td>\n", "      <td id=\"T_5ccfa_row63_col3\" class=\"data row63 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row63_col4\" class=\"data row63 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row63_col5\" class=\"data row63 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row64\" class=\"row_heading level0 row64\" >64</th>\n", "      <td id=\"T_5ccfa_row64_col0\" class=\"data row64 col0\" >64</td>\n", "      <td id=\"T_5ccfa_row64_col1\" class=\"data row64 col1\" >23</td>\n", "      <td id=\"T_5ccfa_row64_col2\" class=\"data row64 col2\" >'8'</td>\n", "      <td id=\"T_5ccfa_row64_col3\" class=\"data row64 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row64_col4\" class=\"data row64 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row64_col5\" class=\"data row64 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row65\" class=\"row_heading level0 row65\" >65</th>\n", "      <td id=\"T_5ccfa_row65_col0\" class=\"data row65 col0\" >65</td>\n", "      <td id=\"T_5ccfa_row65_col1\" class=\"data row65 col1\" >31716</td>\n", "      <td id=\"T_5ccfa_row65_col2\" class=\"data row65 col2\" >'}$'</td>\n", "      <td id=\"T_5ccfa_row65_col3\" class=\"data row65 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row65_col4\" class=\"data row65 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row65_col5\" class=\"data row65 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row66\" class=\"row_heading level0 row66\" >66</th>\n", "      <td id=\"T_5ccfa_row66_col0\" class=\"data row66 col0\" >66</td>\n", "      <td id=\"T_5ccfa_row66_col1\" class=\"data row66 col1\" >659</td>\n", "      <td id=\"T_5ccfa_row66_col2\" class=\"data row66 col2\" >' .'</td>\n", "      <td id=\"T_5ccfa_row66_col3\" class=\"data row66 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row66_col4\" class=\"data row66 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row66_col5\" class=\"data row66 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row67\" class=\"row_heading level0 row67\" >67</th>\n", "      <td id=\"T_5ccfa_row67_col0\" class=\"data row67 col0\" >67</td>\n", "      <td id=\"T_5ccfa_row67_col1\" class=\"data row67 col1\" >7379</td>\n", "      <td id=\"T_5ccfa_row67_col2\" class=\"data row67 col2\" >' Find'</td>\n", "      <td id=\"T_5ccfa_row67_col3\" class=\"data row67 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row67_col4\" class=\"data row67 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row67_col5\" class=\"data row67 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row68\" class=\"row_heading level0 row68\" >68</th>\n", "      <td id=\"T_5ccfa_row68_col0\" class=\"data row68 col0\" >68</td>\n", "      <td id=\"T_5ccfa_row68_col1\" class=\"data row68 col1\" >400</td>\n", "      <td id=\"T_5ccfa_row68_col2\" class=\"data row68 col2\" >' $'</td>\n", "      <td id=\"T_5ccfa_row68_col3\" class=\"data row68 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row68_col4\" class=\"data row68 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row68_col5\" class=\"data row68 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row69\" class=\"row_heading level0 row69\" >69</th>\n", "      <td id=\"T_5ccfa_row69_col0\" class=\"data row69 col0\" >69</td>\n", "      <td id=\"T_5ccfa_row69_col1\" class=\"data row69 col1\" >76</td>\n", "      <td id=\"T_5ccfa_row69_col2\" class=\"data row69 col2\" >'m'</td>\n", "      <td id=\"T_5ccfa_row69_col3\" class=\"data row69 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row69_col4\" class=\"data row69 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row69_col5\" class=\"data row69 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row70\" class=\"row_heading level0 row70\" >70</th>\n", "      <td id=\"T_5ccfa_row70_col0\" class=\"data row70 col0\" >70</td>\n", "      <td id=\"T_5ccfa_row70_col1\" class=\"data row70 col1\" >488</td>\n", "      <td id=\"T_5ccfa_row70_col2\" class=\"data row70 col2\" >' +'</td>\n", "      <td id=\"T_5ccfa_row70_col3\" class=\"data row70 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row70_col4\" class=\"data row70 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row70_col5\" class=\"data row70 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row71\" class=\"row_heading level0 row71\" >71</th>\n", "      <td id=\"T_5ccfa_row71_col0\" class=\"data row71 col0\" >71</td>\n", "      <td id=\"T_5ccfa_row71_col1\" class=\"data row71 col1\" >308</td>\n", "      <td id=\"T_5ccfa_row71_col2\" class=\"data row71 col2\" >' n'</td>\n", "      <td id=\"T_5ccfa_row71_col3\" class=\"data row71 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row71_col4\" class=\"data row71 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row71_col5\" class=\"data row71 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row72\" class=\"row_heading level0 row72\" >72</th>\n", "      <td id=\"T_5ccfa_row72_col0\" class=\"data row72 col0\" >72</td>\n", "      <td id=\"T_5ccfa_row72_col1\" class=\"data row72 col1\" >3</td>\n", "      <td id=\"T_5ccfa_row72_col2\" class=\"data row72 col2\" >'$'</td>\n", "      <td id=\"T_5ccfa_row72_col3\" class=\"data row72 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row72_col4\" class=\"data row72 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row72_col5\" class=\"data row72 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row73\" class=\"row_heading level0 row73\" >73</th>\n", "      <td id=\"T_5ccfa_row73_col0\" class=\"data row73 col0\" >73</td>\n", "      <td id=\"T_5ccfa_row73_col1\" class=\"data row73 col1\" >659</td>\n", "      <td id=\"T_5ccfa_row73_col2\" class=\"data row73 col2\" >' .'</td>\n", "      <td id=\"T_5ccfa_row73_col3\" class=\"data row73 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row73_col4\" class=\"data row73 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row73_col5\" class=\"data row73 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row74\" class=\"row_heading level0 row74\" >74</th>\n", "      <td id=\"T_5ccfa_row74_col0\" class=\"data row74 col0\" >74</td>\n", "      <td id=\"T_5ccfa_row74_col1\" class=\"data row74 col1\" >151645</td>\n", "      <td id=\"T_5ccfa_row74_col2\" class=\"data row74 col2\" >'<|im_end|>'</td>\n", "      <td id=\"T_5ccfa_row74_col3\" class=\"data row74 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row74_col4\" class=\"data row74 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row74_col5\" class=\"data row74 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row75\" class=\"row_heading level0 row75\" >75</th>\n", "      <td id=\"T_5ccfa_row75_col0\" class=\"data row75 col0\" >75</td>\n", "      <td id=\"T_5ccfa_row75_col1\" class=\"data row75 col1\" >198</td>\n", "      <td id=\"T_5ccfa_row75_col2\" class=\"data row75 col2\" >'\\n'</td>\n", "      <td id=\"T_5ccfa_row75_col3\" class=\"data row75 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row75_col4\" class=\"data row75 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row75_col5\" class=\"data row75 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row76\" class=\"row_heading level0 row76\" >76</th>\n", "      <td id=\"T_5ccfa_row76_col0\" class=\"data row76 col0\" >76</td>\n", "      <td id=\"T_5ccfa_row76_col1\" class=\"data row76 col1\" >151644</td>\n", "      <td id=\"T_5ccfa_row76_col2\" class=\"data row76 col2\" >'<|im_start|>'</td>\n", "      <td id=\"T_5ccfa_row76_col3\" class=\"data row76 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row76_col4\" class=\"data row76 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row76_col5\" class=\"data row76 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row77\" class=\"row_heading level0 row77\" >77</th>\n", "      <td id=\"T_5ccfa_row77_col0\" class=\"data row77 col0\" >77</td>\n", "      <td id=\"T_5ccfa_row77_col1\" class=\"data row77 col1\" >77091</td>\n", "      <td id=\"T_5ccfa_row77_col2\" class=\"data row77 col2\" >'assistant'</td>\n", "      <td id=\"T_5ccfa_row77_col3\" class=\"data row77 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row77_col4\" class=\"data row77 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row77_col5\" class=\"data row77 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row78\" class=\"row_heading level0 row78\" >78</th>\n", "      <td id=\"T_5ccfa_row78_col0\" class=\"data row78 col0\" >78</td>\n", "      <td id=\"T_5ccfa_row78_col1\" class=\"data row78 col1\" >198</td>\n", "      <td id=\"T_5ccfa_row78_col2\" class=\"data row78 col2\" >'\\n'</td>\n", "      <td id=\"T_5ccfa_row78_col3\" class=\"data row78 col3\" >-100</td>\n", "      <td id=\"T_5ccfa_row78_col4\" class=\"data row78 col4\" >MASKED</td>\n", "      <td id=\"T_5ccfa_row78_col5\" class=\"data row78 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row79\" class=\"row_heading level0 row79\" >79</th>\n", "      <td id=\"T_5ccfa_row79_col0\" class=\"data row79 col0\" >79</td>\n", "      <td id=\"T_5ccfa_row79_col1\" class=\"data row79 col1\" >32313</td>\n", "      <td id=\"T_5ccfa_row79_col2\" class=\"data row79 col2\" >'Okay'</td>\n", "      <td id=\"T_5ccfa_row79_col3\" class=\"data row79 col3\" >32313</td>\n", "      <td id=\"T_5ccfa_row79_col4\" class=\"data row79 col4\" >TRAINED</td>\n", "      <td id=\"T_5ccfa_row79_col5\" class=\"data row79 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row80\" class=\"row_heading level0 row80\" >80</th>\n", "      <td id=\"T_5ccfa_row80_col0\" class=\"data row80 col0\" >80</td>\n", "      <td id=\"T_5ccfa_row80_col1\" class=\"data row80 col1\" >11</td>\n", "      <td id=\"T_5ccfa_row80_col2\" class=\"data row80 col2\" >','</td>\n", "      <td id=\"T_5ccfa_row80_col3\" class=\"data row80 col3\" >11</td>\n", "      <td id=\"T_5ccfa_row80_col4\" class=\"data row80 col4\" >TRAINED</td>\n", "      <td id=\"T_5ccfa_row80_col5\" class=\"data row80 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row81\" class=\"row_heading level0 row81\" >81</th>\n", "      <td id=\"T_5ccfa_row81_col0\" class=\"data row81 col0\" >81</td>\n", "      <td id=\"T_5ccfa_row81_col1\" class=\"data row81 col1\" >773</td>\n", "      <td id=\"T_5ccfa_row81_col2\" class=\"data row81 col2\" >' so'</td>\n", "      <td id=\"T_5ccfa_row81_col3\" class=\"data row81 col3\" >773</td>\n", "      <td id=\"T_5ccfa_row81_col4\" class=\"data row81 col4\" >TRAINED</td>\n", "      <td id=\"T_5ccfa_row81_col5\" class=\"data row81 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row82\" class=\"row_heading level0 row82\" >82</th>\n", "      <td id=\"T_5ccfa_row82_col0\" class=\"data row82 col0\" >82</td>\n", "      <td id=\"T_5ccfa_row82_col1\" class=\"data row82 col1\" >23084</td>\n", "      <td id=\"T_5ccfa_row82_col2\" class=\"data row82 col2\" >' 이'</td>\n", "      <td id=\"T_5ccfa_row82_col3\" class=\"data row82 col3\" >23084</td>\n", "      <td id=\"T_5ccfa_row82_col4\" class=\"data row82 col4\" >TRAINED</td>\n", "      <td id=\"T_5ccfa_row82_col5\" class=\"data row82 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row83\" class=\"row_heading level0 row83\" >83</th>\n", "      <td id=\"T_5ccfa_row83_col0\" class=\"data row83 col0\" >83</td>\n", "      <td id=\"T_5ccfa_row83_col1\" class=\"data row83 col1\" >18927</td>\n", "      <td id=\"T_5ccfa_row83_col2\" class=\"data row83 col2\" >' probability'</td>\n", "      <td id=\"T_5ccfa_row83_col3\" class=\"data row83 col3\" >18927</td>\n", "      <td id=\"T_5ccfa_row83_col4\" class=\"data row83 col4\" >TRAINED</td>\n", "      <td id=\"T_5ccfa_row83_col5\" class=\"data row83 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row84\" class=\"row_heading level0 row84\" >84</th>\n", "      <td id=\"T_5ccfa_row84_col0\" class=\"data row84 col0\" >84</td>\n", "      <td id=\"T_5ccfa_row84_col1\" class=\"data row84 col1\" >3491</td>\n", "      <td id=\"T_5ccfa_row84_col2\" class=\"data row84 col2\" >' problem'</td>\n", "      <td id=\"T_5ccfa_row84_col3\" class=\"data row84 col3\" >3491</td>\n", "      <td id=\"T_5ccfa_row84_col4\" class=\"data row84 col4\" >TRAINED</td>\n", "      <td id=\"T_5ccfa_row84_col5\" class=\"data row84 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row85\" class=\"row_heading level0 row85\" >85</th>\n", "      <td id=\"T_5ccfa_row85_col0\" class=\"data row85 col0\" >85</td>\n", "      <td id=\"T_5ccfa_row85_col1\" class=\"data row85 col1\" >12802</td>\n", "      <td id=\"T_5ccfa_row85_col2\" class=\"data row85 col2\" >'이'</td>\n", "      <td id=\"T_5ccfa_row85_col3\" class=\"data row85 col3\" >12802</td>\n", "      <td id=\"T_5ccfa_row85_col4\" class=\"data row85 col4\" >TRAINED</td>\n", "      <td id=\"T_5ccfa_row85_col5\" class=\"data row85 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row86\" class=\"row_heading level0 row86\" >86</th>\n", "      <td id=\"T_5ccfa_row86_col0\" class=\"data row86 col0\" >86</td>\n", "      <td id=\"T_5ccfa_row86_col1\" class=\"data row86 col1\" >90686</td>\n", "      <td id=\"T_5ccfa_row86_col2\" class=\"data row86 col2\" >' 있다'</td>\n", "      <td id=\"T_5ccfa_row86_col3\" class=\"data row86 col3\" >90686</td>\n", "      <td id=\"T_5ccfa_row86_col4\" class=\"data row86 col4\" >TRAINED</td>\n", "      <td id=\"T_5ccfa_row86_col5\" class=\"data row86 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row87\" class=\"row_heading level0 row87\" >87</th>\n", "      <td id=\"T_5ccfa_row87_col0\" class=\"data row87 col0\" >87</td>\n", "      <td id=\"T_5ccfa_row87_col1\" class=\"data row87 col1\" >624</td>\n", "      <td id=\"T_5ccfa_row87_col2\" class=\"data row87 col2\" >'.\\n'</td>\n", "      <td id=\"T_5ccfa_row87_col3\" class=\"data row87 col3\" >624</td>\n", "      <td id=\"T_5ccfa_row87_col4\" class=\"data row87 col4\" >TRAINED</td>\n", "      <td id=\"T_5ccfa_row87_col5\" class=\"data row87 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row88\" class=\"row_heading level0 row88\" >88</th>\n", "      <td id=\"T_5ccfa_row88_col0\" class=\"data row88 col0\" >88</td>\n", "      <td id=\"T_5ccfa_row88_col1\" class=\"data row88 col1\" >2132</td>\n", "      <td id=\"T_5ccfa_row88_col2\" class=\"data row88 col2\" >'It'</td>\n", "      <td id=\"T_5ccfa_row88_col3\" class=\"data row88 col3\" >2132</td>\n", "      <td id=\"T_5ccfa_row88_col4\" class=\"data row88 col4\" >TRAINED</td>\n", "      <td id=\"T_5ccfa_row88_col5\" class=\"data row88 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row89\" class=\"row_heading level0 row89\" >89</th>\n", "      <td id=\"T_5ccfa_row89_col0\" class=\"data row89 col0\" >89</td>\n", "      <td id=\"T_5ccfa_row89_col1\" class=\"data row89 col1\" >2727</td>\n", "      <td id=\"T_5ccfa_row89_col2\" class=\"data row89 col2\" >' says'</td>\n", "      <td id=\"T_5ccfa_row89_col3\" class=\"data row89 col3\" >2727</td>\n", "      <td id=\"T_5ccfa_row89_col4\" class=\"data row89 col4\" >TRAINED</td>\n", "      <td id=\"T_5ccfa_row89_col5\" class=\"data row89 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row90\" class=\"row_heading level0 row90\" >90</th>\n", "      <td id=\"T_5ccfa_row90_col0\" class=\"data row90 col0\" >90</td>\n", "      <td id=\"T_5ccfa_row90_col1\" class=\"data row90 col1\" >18927</td>\n", "      <td id=\"T_5ccfa_row90_col2\" class=\"data row90 col2\" >' probability'</td>\n", "      <td id=\"T_5ccfa_row90_col3\" class=\"data row90 col3\" >18927</td>\n", "      <td id=\"T_5ccfa_row90_col4\" class=\"data row90 col4\" >TRAINED</td>\n", "      <td id=\"T_5ccfa_row90_col5\" class=\"data row90 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row91\" class=\"row_heading level0 row91\" >91</th>\n", "      <td id=\"T_5ccfa_row91_col0\" class=\"data row91 col0\" >91</td>\n", "      <td id=\"T_5ccfa_row91_col1\" class=\"data row91 col1\" >429</td>\n", "      <td id=\"T_5ccfa_row91_col2\" class=\"data row91 col2\" >' that'</td>\n", "      <td id=\"T_5ccfa_row91_col3\" class=\"data row91 col3\" >429</td>\n", "      <td id=\"T_5ccfa_row91_col4\" class=\"data row91 col4\" >TRAINED</td>\n", "      <td id=\"T_5ccfa_row91_col5\" class=\"data row91 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row92\" class=\"row_heading level0 row92\" >92</th>\n", "      <td id=\"T_5ccfa_row92_col0\" class=\"data row92 col0\" >92</td>\n", "      <td id=\"T_5ccfa_row92_col1\" class=\"data row92 col1\" >264</td>\n", "      <td id=\"T_5ccfa_row92_col2\" class=\"data row92 col2\" >' a'</td>\n", "      <td id=\"T_5ccfa_row92_col3\" class=\"data row92 col3\" >264</td>\n", "      <td id=\"T_5ccfa_row92_col4\" class=\"data row92 col4\" >TRAINED</td>\n", "      <td id=\"T_5ccfa_row92_col5\" class=\"data row92 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row93\" class=\"row_heading level0 row93\" >93</th>\n", "      <td id=\"T_5ccfa_row93_col0\" class=\"data row93 col0\" >93</td>\n", "      <td id=\"T_5ccfa_row93_col1\" class=\"data row93 col1\" >26618</td>\n", "      <td id=\"T_5ccfa_row93_col2\" class=\"data row93 col2\" >' randomly'</td>\n", "      <td id=\"T_5ccfa_row93_col3\" class=\"data row93 col3\" >26618</td>\n", "      <td id=\"T_5ccfa_row93_col4\" class=\"data row93 col4\" >TRAINED</td>\n", "      <td id=\"T_5ccfa_row93_col5\" class=\"data row93 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row94\" class=\"row_heading level0 row94\" >94</th>\n", "      <td id=\"T_5ccfa_row94_col0\" class=\"data row94 col0\" >94</td>\n", "      <td id=\"T_5ccfa_row94_col1\" class=\"data row94 col1\" >11882</td>\n", "      <td id=\"T_5ccfa_row94_col2\" class=\"data row94 col2\" >' chosen'</td>\n", "      <td id=\"T_5ccfa_row94_col3\" class=\"data row94 col3\" >11882</td>\n", "      <td id=\"T_5ccfa_row94_col4\" class=\"data row94 col4\" >TRAINED</td>\n", "      <td id=\"T_5ccfa_row94_col5\" class=\"data row94 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row95\" class=\"row_heading level0 row95\" >95</th>\n", "      <td id=\"T_5ccfa_row95_col0\" class=\"data row95 col0\" >95</td>\n", "      <td id=\"T_5ccfa_row95_col1\" class=\"data row95 col1\" >6785</td>\n", "      <td id=\"T_5ccfa_row95_col2\" class=\"data row95 col2\" >' positive'</td>\n", "      <td id=\"T_5ccfa_row95_col3\" class=\"data row95 col3\" >6785</td>\n", "      <td id=\"T_5ccfa_row95_col4\" class=\"data row95 col4\" >TRAINED</td>\n", "      <td id=\"T_5ccfa_row95_col5\" class=\"data row95 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row96\" class=\"row_heading level0 row96\" >96</th>\n", "      <td id=\"T_5ccfa_row96_col0\" class=\"data row96 col0\" >96</td>\n", "      <td id=\"T_5ccfa_row96_col1\" class=\"data row96 col1\" >49109</td>\n", "      <td id=\"T_5ccfa_row96_col2\" class=\"data row96 col2\" >' divisor'</td>\n", "      <td id=\"T_5ccfa_row96_col3\" class=\"data row96 col3\" >49109</td>\n", "      <td id=\"T_5ccfa_row96_col4\" class=\"data row96 col4\" >TRAINED</td>\n", "      <td id=\"T_5ccfa_row96_col5\" class=\"data row96 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row97\" class=\"row_heading level0 row97\" >97</th>\n", "      <td id=\"T_5ccfa_row97_col0\" class=\"data row97 col0\" >97</td>\n", "      <td id=\"T_5ccfa_row97_col1\" class=\"data row97 col1\" >315</td>\n", "      <td id=\"T_5ccfa_row97_col2\" class=\"data row97 col2\" >' of'</td>\n", "      <td id=\"T_5ccfa_row97_col3\" class=\"data row97 col3\" >315</td>\n", "      <td id=\"T_5ccfa_row97_col4\" class=\"data row97 col4\" >TRAINED</td>\n", "      <td id=\"T_5ccfa_row97_col5\" class=\"data row97 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row98\" class=\"row_heading level0 row98\" >98</th>\n", "      <td id=\"T_5ccfa_row98_col0\" class=\"data row98 col0\" >98</td>\n", "      <td id=\"T_5ccfa_row98_col1\" class=\"data row98 col1\" >17767</td>\n", "      <td id=\"T_5ccfa_row98_col2\" class=\"data row98 col2\" >' \\\\('</td>\n", "      <td id=\"T_5ccfa_row98_col3\" class=\"data row98 col3\" >17767</td>\n", "      <td id=\"T_5ccfa_row98_col4\" class=\"data row98 col4\" >TRAINED</td>\n", "      <td id=\"T_5ccfa_row98_col5\" class=\"data row98 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_5ccfa_level0_row99\" class=\"row_heading level0 row99\" >99</th>\n", "      <td id=\"T_5ccfa_row99_col0\" class=\"data row99 col0\" >99</td>\n", "      <td id=\"T_5ccfa_row99_col1\" class=\"data row99 col1\" >16</td>\n", "      <td id=\"T_5ccfa_row99_col2\" class=\"data row99 col2\" >'1'</td>\n", "      <td id=\"T_5ccfa_row99_col3\" class=\"data row99 col3\" >16</td>\n", "      <td id=\"T_5ccfa_row99_col4\" class=\"data row99 col4\" >TRAINED</td>\n", "      <td id=\"T_5ccfa_row99_col5\" class=\"data row99 col5\" >✅</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x7f43241c3f70>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def show_detailed_token_analysis(sample_data, max_tokens=50):\n", "    \"\"\"Show detailed token-by-token analysis with decoded values and -100 labels.\"\"\"\n", "    \n", "    input_ids = sample_data['input_ids']\n", "    labels = sample_data['labels']\n", "    \n", "    print(f\"\\n🔍 DETAILED TOKEN ANALYSIS (First {max_tokens} tokens)\")\n", "    print(\"=\" * 100)\n", "    print(\"This shows exactly what goes into the model:\")\n", "    print(\"- input_ids: The actual tokens fed to the model\")\n", "    print(\"- labels: What the model tries to predict (-100 = masked, token_id = trained)\")\n", "    print(\"=\" * 100)\n", "    \n", "    # Create detailed analysis table\n", "    rows = []\n", "    \n", "    for i in range(min(max_tokens, len(input_ids))):\n", "        token_id = input_ids[i]\n", "        label = labels[i]\n", "        token_text = tokenizer.decode([token_id])\n", "        \n", "        # Determine status\n", "        if label == -100:\n", "            status = \"MASKED\"\n", "            label_display = \"-100\"\n", "        else:\n", "            status = \"TRAINED\"\n", "            label_display = str(label)\n", "        \n", "        rows.append({\n", "            'Position': i,\n", "            'Input Token ID': token_id,\n", "            'Decoded Text': repr(token_text),\n", "            'Label': label_display,\n", "            'Status': status,\n", "            'Contributes to Loss': \"❌\" if label == -100 else \"✅\"\n", "        })\n", "    \n", "    df = pd.DataFrame(rows)\n", "    \n", "    # Style the dataframe\n", "    def highlight_status(val):\n", "        if val == 'MASKED':\n", "            return 'background-color: #ffcccc; color: #cc0000; font-weight: bold'\n", "        elif val == 'TRAINED':\n", "            return 'background-color: #ccffcc; color: #006600; font-weight: bold'\n", "        return ''\n", "    \n", "    def highlight_label(val):\n", "        if val == '-100':\n", "            return 'background-color: #ffcccc; color: #cc0000; font-weight: bold'\n", "        return 'background-color: #ccffcc; color: #006600'\n", "    \n", "    styled_df = df.style.applymap(highlight_status, subset=['Status']) \\\n", "                      .applymap(highlight_label, subset=['Label'])\n", "    \n", "    display(styled_df)\n", "    \n", "    return df\n", "\n", "# Show detailed token analysis\n", "token_df = show_detailed_token_analysis(sample_data, max_tokens=100)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Last 50 Tokens Analysis (Check for EOS Token)"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🔚 LAST 50 TOKENS ANALYSIS (to check for EOS token)\n", "====================================================================================================\n", "Total tokens in sequence: 1769\n", "Showing tokens 1719 to 1768\n", "EOS token ID: 151645 ('<|im_end|>')\n", "====================================================================================================\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/tmp/ipykernel_2977142/3675267896.py:65: FutureWarning: Styler.applymap has been deprecated. Use Styler.map instead.\n", "  styled_last_df = last_df.style.applymap(highlight_status, subset=['Status']) \\\n"]}, {"data": {"text/html": ["<style type=\"text/css\">\n", "#T_42a22_row0_col3, #T_42a22_row1_col3, #T_42a22_row2_col3, #T_42a22_row3_col3, #T_42a22_row4_col3, #T_42a22_row5_col3, #T_42a22_row6_col3, #T_42a22_row7_col3, #T_42a22_row8_col3, #T_42a22_row9_col3, #T_42a22_row10_col3, #T_42a22_row11_col3, #T_42a22_row12_col3, #T_42a22_row13_col3, #T_42a22_row14_col3, #T_42a22_row15_col3, #T_42a22_row16_col3, #T_42a22_row17_col3, #T_42a22_row18_col3, #T_42a22_row19_col3, #T_42a22_row20_col3, #T_42a22_row21_col3, #T_42a22_row22_col3, #T_42a22_row23_col3, #T_42a22_row24_col3, #T_42a22_row25_col3, #T_42a22_row26_col3, #T_42a22_row27_col3, #T_42a22_row28_col3, #T_42a22_row29_col3, #T_42a22_row30_col3, #T_42a22_row31_col3, #T_42a22_row32_col3, #T_42a22_row33_col3, #T_42a22_row34_col3, #T_42a22_row35_col3, #T_42a22_row36_col3, #T_42a22_row37_col3, #T_42a22_row38_col3, #T_42a22_row39_col3, #T_42a22_row40_col3, #T_42a22_row41_col3, #T_42a22_row42_col3, #T_42a22_row43_col3, #T_42a22_row44_col3, #T_42a22_row45_col3, #T_42a22_row46_col3, #T_42a22_row49_col3 {\n", "  background-color: #ccffcc;\n", "  color: #006600;\n", "}\n", "#T_42a22_row0_col4, #T_42a22_row1_col4, #T_42a22_row2_col4, #T_42a22_row3_col4, #T_42a22_row4_col4, #T_42a22_row5_col4, #T_42a22_row6_col4, #T_42a22_row7_col4, #T_42a22_row8_col4, #T_42a22_row9_col4, #T_42a22_row10_col4, #T_42a22_row11_col4, #T_42a22_row12_col4, #T_42a22_row13_col4, #T_42a22_row14_col4, #T_42a22_row15_col4, #T_42a22_row16_col4, #T_42a22_row17_col4, #T_42a22_row18_col4, #T_42a22_row19_col4, #T_42a22_row20_col4, #T_42a22_row21_col4, #T_42a22_row22_col4, #T_42a22_row23_col4, #T_42a22_row24_col4, #T_42a22_row25_col4, #T_42a22_row26_col4, #T_42a22_row27_col4, #T_42a22_row28_col4, #T_42a22_row29_col4, #T_42a22_row30_col4, #T_42a22_row31_col4, #T_42a22_row32_col4, #T_42a22_row33_col4, #T_42a22_row34_col4, #T_42a22_row35_col4, #T_42a22_row36_col4, #T_42a22_row37_col4, #T_42a22_row38_col4, #T_42a22_row39_col4, #T_42a22_row40_col4, #T_42a22_row41_col4, #T_42a22_row42_col4, #T_42a22_row43_col4, #T_42a22_row44_col4, #T_42a22_row45_col4, #T_42a22_row46_col4, #T_42a22_row49_col4 {\n", "  background-color: #ccffcc;\n", "  color: #006600;\n", "  font-weight: bold;\n", "}\n", "#T_42a22_row47_col2, #T_42a22_row49_col2 {\n", "  background-color: #ffffcc;\n", "  color: #cc6600;\n", "  font-weight: bold;\n", "  border: 2px solid #cc6600;\n", "}\n", "#T_42a22_row47_col3, #T_42a22_row47_col4, #T_42a22_row48_col3, #T_42a22_row48_col4 {\n", "  background-color: #ffcccc;\n", "  color: #cc0000;\n", "  font-weight: bold;\n", "}\n", "</style>\n", "<table id=\"T_42a22\">\n", "  <thead>\n", "    <tr>\n", "      <th class=\"blank level0\" >&nbsp;</th>\n", "      <th id=\"T_42a22_level0_col0\" class=\"col_heading level0 col0\" >Position</th>\n", "      <th id=\"T_42a22_level0_col1\" class=\"col_heading level0 col1\" >Input Token ID</th>\n", "      <th id=\"T_42a22_level0_col2\" class=\"col_heading level0 col2\" >Decoded Text</th>\n", "      <th id=\"T_42a22_level0_col3\" class=\"col_heading level0 col3\" >Label</th>\n", "      <th id=\"T_42a22_level0_col4\" class=\"col_heading level0 col4\" >Status</th>\n", "      <th id=\"T_42a22_level0_col5\" class=\"col_heading level0 col5\" >Contributes to Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row0\" class=\"row_heading level0 row0\" >0</th>\n", "      <td id=\"T_42a22_row0_col0\" class=\"data row0 col0\" >1719</td>\n", "      <td id=\"T_42a22_row0_col1\" class=\"data row0 col1\" >4194</td>\n", "      <td id=\"T_42a22_row0_col2\" class=\"data row0 col2\" >' random'</td>\n", "      <td id=\"T_42a22_row0_col3\" class=\"data row0 col3\" >4194</td>\n", "      <td id=\"T_42a22_row0_col4\" class=\"data row0 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row0_col5\" class=\"data row0 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row1\" class=\"row_heading level0 row1\" >1</th>\n", "      <td id=\"T_42a22_row1_col0\" class=\"data row1 col0\" >1720</td>\n", "      <td id=\"T_42a22_row1_col1\" class=\"data row1 col1\" >12802</td>\n", "      <td id=\"T_42a22_row1_col2\" class=\"data row1 col2\" >'이'</td>\n", "      <td id=\"T_42a22_row1_col3\" class=\"data row1 col3\" >12802</td>\n", "      <td id=\"T_42a22_row1_col4\" class=\"data row1 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row1_col5\" class=\"data row1 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row2\" class=\"row_heading level0 row2\" >2</th>\n", "      <td id=\"T_42a22_row2_col0\" class=\"data row2 col0\" >1721</td>\n", "      <td id=\"T_42a22_row2_col1\" class=\"data row2 col1\" >83036</td>\n", "      <td id=\"T_42a22_row2_col2\" class=\"data row2 col2\" >'니'</td>\n", "      <td id=\"T_42a22_row2_col3\" class=\"data row2 col3\" >83036</td>\n", "      <td id=\"T_42a22_row2_col4\" class=\"data row2 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row2_col5\" class=\"data row2 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row3\" class=\"row_heading level0 row3\" >3</th>\n", "      <td id=\"T_42a22_row3_col0\" class=\"data row3 col0\" >1722</td>\n", "      <td id=\"T_42a22_row3_col1\" class=\"data row3 col1\" >94203</td>\n", "      <td id=\"T_42a22_row3_col2\" class=\"data row3 col2\" >' 계'</td>\n", "      <td id=\"T_42a22_row3_col3\" class=\"data row3 col3\" >94203</td>\n", "      <td id=\"T_42a22_row3_col4\" class=\"data row3 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row3_col5\" class=\"data row3 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row4\" class=\"row_heading level0 row4\" >4</th>\n", "      <td id=\"T_42a22_row4_col0\" class=\"data row4 col0\" >1723</td>\n", "      <td id=\"T_42a22_row4_col1\" class=\"data row4 col1\" >85057</td>\n", "      <td id=\"T_42a22_row4_col2\" class=\"data row4 col2\" >'산'</td>\n", "      <td id=\"T_42a22_row4_col3\" class=\"data row4 col3\" >85057</td>\n", "      <td id=\"T_42a22_row4_col4\" class=\"data row4 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row4_col5\" class=\"data row4 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row5\" class=\"row_heading level0 row5\" >5</th>\n", "      <td id=\"T_42a22_row5_col0\" class=\"data row5 col0\" >1724</td>\n", "      <td id=\"T_42a22_row5_col1\" class=\"data row5 col1\" >131417</td>\n", "      <td id=\"T_42a22_row5_col2\" class=\"data row5 col2\" >' 맞'</td>\n", "      <td id=\"T_42a22_row5_col3\" class=\"data row5 col3\" >131417</td>\n", "      <td id=\"T_42a22_row5_col4\" class=\"data row5 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row5_col5\" class=\"data row5 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row6\" class=\"row_heading level0 row6\" >6</th>\n", "      <td id=\"T_42a22_row6_col0\" class=\"data row6 col0\" >1725</td>\n", "      <td id=\"T_42a22_row6_col1\" class=\"data row6 col1\" >13146</td>\n", "      <td id=\"T_42a22_row6_col2\" class=\"data row6 col2\" >'다'</td>\n", "      <td id=\"T_42a22_row6_col3\" class=\"data row6 col3\" >13146</td>\n", "      <td id=\"T_42a22_row6_col4\" class=\"data row6 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row6_col5\" class=\"data row6 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row7\" class=\"row_heading level0 row7\" >7</th>\n", "      <td id=\"T_42a22_row7_col0\" class=\"data row7 col0\" >1726</td>\n", "      <td id=\"T_42a22_row7_col1\" class=\"data row7 col1\" >624</td>\n", "      <td id=\"T_42a22_row7_col2\" class=\"data row7 col2\" >'.\\n'</td>\n", "      <td id=\"T_42a22_row7_col3\" class=\"data row7 col3\" >624</td>\n", "      <td id=\"T_42a22_row7_col4\" class=\"data row7 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row7_col5\" class=\"data row7 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row8\" class=\"row_heading level0 row8\" >8</th>\n", "      <td id=\"T_42a22_row8_col0\" class=\"data row8 col0\" >1727</td>\n", "      <td id=\"T_42a22_row8_col1\" class=\"data row8 col1\" >48606</td>\n", "      <td id=\"T_42a22_row8_col2\" class=\"data row8 col2\" >'그'</td>\n", "      <td id=\"T_42a22_row8_col3\" class=\"data row8 col3\" >48606</td>\n", "      <td id=\"T_42a22_row8_col4\" class=\"data row8 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row8_col5\" class=\"data row8 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row9\" class=\"row_heading level0 row9\" >9</th>\n", "      <td id=\"T_42a22_row9_col0\" class=\"data row9 col0\" >1728</td>\n", "      <td id=\"T_42a22_row9_col1\" class=\"data row9 col1\" >60294</td>\n", "      <td id=\"T_42a22_row9_col2\" class=\"data row9 col2\" >'러'</td>\n", "      <td id=\"T_42a22_row9_col3\" class=\"data row9 col3\" >60294</td>\n", "      <td id=\"T_42a22_row9_col4\" class=\"data row9 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row9_col5\" class=\"data row9 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row10\" class=\"row_heading level0 row10\" >10</th>\n", "      <td id=\"T_42a22_row10_col0\" class=\"data row10 col0\" >1729</td>\n", "      <td id=\"T_42a22_row10_col1\" class=\"data row10 col1\" >83036</td>\n", "      <td id=\"T_42a22_row10_col2\" class=\"data row10 col2\" >'니'</td>\n", "      <td id=\"T_42a22_row10_col3\" class=\"data row10 col3\" >83036</td>\n", "      <td id=\"T_42a22_row10_col4\" class=\"data row10 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row10_col5\" class=\"data row10 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row11\" class=\"row_heading level0 row11\" >11</th>\n", "      <td id=\"T_42a22_row11_col0\" class=\"data row11 col0\" >1730</td>\n", "      <td id=\"T_42a22_row11_col1\" class=\"data row11 col1\" >1760</td>\n", "      <td id=\"T_42a22_row11_col2\" class=\"data row11 col2\" >' count'</td>\n", "      <td id=\"T_42a22_row11_col3\" class=\"data row11 col3\" >1760</td>\n", "      <td id=\"T_42a22_row11_col4\" class=\"data row11 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row11_col5\" class=\"data row11 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row12\" class=\"row_heading level0 row12\" >12</th>\n", "      <td id=\"T_42a22_row12_col0\" class=\"data row12 col0\" >1731</td>\n", "      <td id=\"T_42a22_row12_col1\" class=\"data row12 col1\" >47985</td>\n", "      <td id=\"T_42a22_row12_col2\" class=\"data row12 col2\" >'도'</td>\n", "      <td id=\"T_42a22_row12_col3\" class=\"data row12 col3\" >47985</td>\n", "      <td id=\"T_42a22_row12_col4\" class=\"data row12 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row12_col5\" class=\"data row12 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row13\" class=\"row_heading level0 row13\" >13</th>\n", "      <td id=\"T_42a22_row13_col0\" class=\"data row13 col0\" >1732</td>\n", "      <td id=\"T_42a22_row13_col1\" class=\"data row13 col1\" >131417</td>\n", "      <td id=\"T_42a22_row13_col2\" class=\"data row13 col2\" >' 맞'</td>\n", "      <td id=\"T_42a22_row13_col3\" class=\"data row13 col3\" >131417</td>\n", "      <td id=\"T_42a22_row13_col4\" class=\"data row13 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row13_col5\" class=\"data row13 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row14\" class=\"row_heading level0 row14\" >14</th>\n", "      <td id=\"T_42a22_row14_col0\" class=\"data row14 col0\" >1733</td>\n", "      <td id=\"T_42a22_row14_col1\" class=\"data row14 col1\" >13146</td>\n", "      <td id=\"T_42a22_row14_col2\" class=\"data row14 col2\" >'다'</td>\n", "      <td id=\"T_42a22_row14_col3\" class=\"data row14 col3\" >13146</td>\n", "      <td id=\"T_42a22_row14_col4\" class=\"data row14 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row14_col5\" class=\"data row14 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row15\" class=\"row_heading level0 row15\" >15</th>\n", "      <td id=\"T_42a22_row15_col0\" class=\"data row15 col0\" >1734</td>\n", "      <td id=\"T_42a22_row15_col1\" class=\"data row15 col1\" >624</td>\n", "      <td id=\"T_42a22_row15_col2\" class=\"data row15 col2\" >'.\\n'</td>\n", "      <td id=\"T_42a22_row15_col3\" class=\"data row15 col3\" >624</td>\n", "      <td id=\"T_42a22_row15_col4\" class=\"data row15 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row15_col5\" class=\"data row15 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row16\" class=\"row_heading level0 row16\" >16</th>\n", "      <td id=\"T_42a22_row16_col0\" class=\"data row16 col0\" >1735</td>\n", "      <td id=\"T_42a22_row16_col1\" class=\"data row16 col1\" >134277</td>\n", "      <td id=\"T_42a22_row16_col2\" class=\"data row16 col2\" >'따'</td>\n", "      <td id=\"T_42a22_row16_col3\" class=\"data row16 col3\" >134277</td>\n", "      <td id=\"T_42a22_row16_col4\" class=\"data row16 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row16_col5\" class=\"data row16 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row17\" class=\"row_heading level0 row17\" >17</th>\n", "      <td id=\"T_42a22_row17_col0\" class=\"data row17 col0\" >1736</td>\n", "      <td id=\"T_42a22_row17_col1\" class=\"data row17 col1\" >50340</td>\n", "      <td id=\"T_42a22_row17_col2\" class=\"data row17 col2\" >'라'</td>\n", "      <td id=\"T_42a22_row17_col3\" class=\"data row17 col3\" >50340</td>\n", "      <td id=\"T_42a22_row17_col4\" class=\"data row17 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row17_col5\" class=\"data row17 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row18\" class=\"row_heading level0 row18\" >18</th>\n", "      <td id=\"T_42a22_row18_col0\" class=\"data row18 col0\" >1737</td>\n", "      <td id=\"T_42a22_row18_col1\" class=\"data row18 col1\" >26698</td>\n", "      <td id=\"T_42a22_row18_col2\" class=\"data row18 col2\" >'서'</td>\n", "      <td id=\"T_42a22_row18_col3\" class=\"data row18 col3\" >26698</td>\n", "      <td id=\"T_42a22_row18_col4\" class=\"data row18 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row18_col5\" class=\"data row18 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row19\" class=\"row_heading level0 row19\" >19</th>\n", "      <td id=\"T_42a22_row19_col0\" class=\"data row19 col0\" >1738</td>\n", "      <td id=\"T_42a22_row19_col1\" class=\"data row19 col1\" >11</td>\n", "      <td id=\"T_42a22_row19_col2\" class=\"data row19 col2\" >','</td>\n", "      <td id=\"T_42a22_row19_col3\" class=\"data row19 col3\" >11</td>\n", "      <td id=\"T_42a22_row19_col4\" class=\"data row19 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row19_col5\" class=\"data row19 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row20\" class=\"row_heading level0 row20\" >20</th>\n", "      <td id=\"T_42a22_row20_col0\" class=\"data row20 col0\" >1739</td>\n", "      <td id=\"T_42a22_row20_col1\" class=\"data row20 col1\" >66136</td>\n", "      <td id=\"T_42a22_row20_col2\" class=\"data row20 col2\" >' 내'</td>\n", "      <td id=\"T_42a22_row20_col3\" class=\"data row20 col3\" >66136</td>\n", "      <td id=\"T_42a22_row20_col4\" class=\"data row20 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row20_col5\" class=\"data row20 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row21\" class=\"row_heading level0 row21\" >21</th>\n", "      <td id=\"T_42a22_row21_col0\" class=\"data row21 col0\" >1740</td>\n", "      <td id=\"T_42a22_row21_col1\" class=\"data row21 col1\" >10764</td>\n", "      <td id=\"T_42a22_row21_col2\" class=\"data row21 col2\" >' �'</td>\n", "      <td id=\"T_42a22_row21_col3\" class=\"data row21 col3\" >10764</td>\n", "      <td id=\"T_42a22_row21_col4\" class=\"data row21 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row21_col5\" class=\"data row21 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row22\" class=\"row_heading level0 row22\" >22</th>\n", "      <td id=\"T_42a22_row22_col0\" class=\"data row22 col0\" >1741</td>\n", "      <td id=\"T_42a22_row22_col1\" class=\"data row22 col1\" >240</td>\n", "      <td id=\"T_42a22_row22_col2\" class=\"data row22 col2\" >'�'</td>\n", "      <td id=\"T_42a22_row22_col3\" class=\"data row22 col3\" >240</td>\n", "      <td id=\"T_42a22_row22_col4\" class=\"data row22 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row22_col5\" class=\"data row22 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row23\" class=\"row_heading level0 row23\" >23</th>\n", "      <td id=\"T_42a22_row23_col0\" class=\"data row23 col0\" >1742</td>\n", "      <td id=\"T_42a22_row23_col1\" class=\"data row23 col1\" >222</td>\n", "      <td id=\"T_42a22_row23_col2\" class=\"data row23 col2\" >'�'</td>\n", "      <td id=\"T_42a22_row23_col3\" class=\"data row23 col3\" >222</td>\n", "      <td id=\"T_42a22_row23_col4\" class=\"data row23 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row23_col5\" class=\"data row23 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row24\" class=\"row_heading level0 row24\" >24</th>\n", "      <td id=\"T_42a22_row24_col0\" class=\"data row24 col0\" >1743</td>\n", "      <td id=\"T_42a22_row24_col1\" class=\"data row24 col1\" >12802</td>\n", "      <td id=\"T_42a22_row24_col2\" class=\"data row24 col2\" >'이'</td>\n", "      <td id=\"T_42a22_row24_col3\" class=\"data row24 col3\" >12802</td>\n", "      <td id=\"T_42a22_row24_col4\" class=\"data row24 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row24_col5\" class=\"data row24 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row25\" class=\"row_heading level0 row25\" >25</th>\n", "      <td id=\"T_42a22_row25_col0\" class=\"data row25 col0\" >1744</td>\n", "      <td id=\"T_42a22_row25_col1\" class=\"data row25 col1\" >18411</td>\n", "      <td id=\"T_42a22_row25_col2\" class=\"data row25 col2\" >'를'</td>\n", "      <td id=\"T_42a22_row25_col3\" class=\"data row25 col3\" >18411</td>\n", "      <td id=\"T_42a22_row25_col4\" class=\"data row25 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row25_col5\" class=\"data row25 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row26\" class=\"row_heading level0 row26\" >26</th>\n", "      <td id=\"T_42a22_row26_col0\" class=\"data row26 col0\" >1745</td>\n", "      <td id=\"T_42a22_row26_col1\" class=\"data row26 col1\" >125713</td>\n", "      <td id=\"T_42a22_row26_col2\" class=\"data row26 col2\" >' �'</td>\n", "      <td id=\"T_42a22_row26_col3\" class=\"data row26 col3\" >125713</td>\n", "      <td id=\"T_42a22_row26_col4\" class=\"data row26 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row26_col5\" class=\"data row26 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row27\" class=\"row_heading level0 row27\" >27</th>\n", "      <td id=\"T_42a22_row27_col0\" class=\"data row27 col0\" >1746</td>\n", "      <td id=\"T_42a22_row27_col1\" class=\"data row27 col1\" >123</td>\n", "      <td id=\"T_42a22_row27_col2\" class=\"data row27 col2\" >'�'</td>\n", "      <td id=\"T_42a22_row27_col3\" class=\"data row27 col3\" >123</td>\n", "      <td id=\"T_42a22_row27_col4\" class=\"data row27 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row27_col5\" class=\"data row27 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row28\" class=\"row_heading level0 row28\" >28</th>\n", "      <td id=\"T_42a22_row28_col0\" class=\"data row28 col0\" >1747</td>\n", "      <td id=\"T_42a22_row28_col1\" class=\"data row28 col1\" >31079</td>\n", "      <td id=\"T_42a22_row28_col2\" class=\"data row28 col2\" >'어'</td>\n", "      <td id=\"T_42a22_row28_col3\" class=\"data row28 col3\" >31079</td>\n", "      <td id=\"T_42a22_row28_col4\" class=\"data row28 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row28_col5\" class=\"data row28 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row29\" class=\"row_heading level0 row29\" >29</th>\n", "      <td id=\"T_42a22_row29_col0\" class=\"data row29 col0\" >1748</td>\n", "      <td id=\"T_42a22_row29_col1\" class=\"data row29 col1\" >47985</td>\n", "      <td id=\"T_42a22_row29_col2\" class=\"data row29 col2\" >'도'</td>\n", "      <td id=\"T_42a22_row29_col3\" class=\"data row29 col3\" >47985</td>\n", "      <td id=\"T_42a22_row29_col4\" class=\"data row29 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row29_col5\" class=\"data row29 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row30\" class=\"row_heading level0 row30\" >30</th>\n", "      <td id=\"T_42a22_row30_col0\" class=\"data row30 col0\" >1749</td>\n", "      <td id=\"T_42a22_row30_col1\" class=\"data row30 col1\" >130723</td>\n", "      <td id=\"T_42a22_row30_col2\" class=\"data row30 col2\" >' 된다'</td>\n", "      <td id=\"T_42a22_row30_col3\" class=\"data row30 col3\" >130723</td>\n", "      <td id=\"T_42a22_row30_col4\" class=\"data row30 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row30_col5\" class=\"data row30 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row31\" class=\"row_heading level0 row31\" >31</th>\n", "      <td id=\"T_42a22_row31_col0\" class=\"data row31 col0\" >1750</td>\n", "      <td id=\"T_42a22_row31_col1\" class=\"data row31 col1\" >624</td>\n", "      <td id=\"T_42a22_row31_col2\" class=\"data row31 col2\" >'.\\n'</td>\n", "      <td id=\"T_42a22_row31_col3\" class=\"data row31 col3\" >624</td>\n", "      <td id=\"T_42a22_row31_col4\" class=\"data row31 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row31_col5\" class=\"data row31 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row32\" class=\"row_heading level0 row32\" >32</th>\n", "      <td id=\"T_42a22_row32_col0\" class=\"data row32 col0\" >1751</td>\n", "      <td id=\"T_42a22_row32_col1\" class=\"data row32 col1\" >132760</td>\n", "      <td id=\"T_42a22_row32_col2\" class=\"data row32 col2\" >'답'</td>\n", "      <td id=\"T_42a22_row32_col3\" class=\"data row32 col3\" >132760</td>\n", "      <td id=\"T_42a22_row32_col4\" class=\"data row32 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row32_col5\" class=\"data row32 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row33\" class=\"row_heading level0 row33\" >33</th>\n", "      <td id=\"T_42a22_row33_col0\" class=\"data row33 col0\" >1752</td>\n", "      <td id=\"T_42a22_row33_col1\" class=\"data row33 col1\" >33704</td>\n", "      <td id=\"T_42a22_row33_col2\" class=\"data row33 col2\" >'은'</td>\n", "      <td id=\"T_42a22_row33_col3\" class=\"data row33 col3\" >33704</td>\n", "      <td id=\"T_42a22_row33_col4\" class=\"data row33 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row33_col5\" class=\"data row33 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row34\" class=\"row_heading level0 row34\" >34</th>\n", "      <td id=\"T_42a22_row34_col0\" class=\"data row34 col0\" >1753</td>\n", "      <td id=\"T_42a22_row34_col1\" class=\"data row34 col1\" >220</td>\n", "      <td id=\"T_42a22_row34_col2\" class=\"data row34 col2\" >' '</td>\n", "      <td id=\"T_42a22_row34_col3\" class=\"data row34 col3\" >220</td>\n", "      <td id=\"T_42a22_row34_col4\" class=\"data row34 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row34_col5\" class=\"data row34 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row35\" class=\"row_heading level0 row35\" >35</th>\n", "      <td id=\"T_42a22_row35_col0\" class=\"data row35 col0\" >1754</td>\n", "      <td id=\"T_42a22_row35_col1\" class=\"data row35 col1\" >21</td>\n", "      <td id=\"T_42a22_row35_col2\" class=\"data row35 col2\" >'6'</td>\n", "      <td id=\"T_42a22_row35_col3\" class=\"data row35 col3\" >21</td>\n", "      <td id=\"T_42a22_row35_col4\" class=\"data row35 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row35_col5\" class=\"data row35 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row36\" class=\"row_heading level0 row36\" >36</th>\n", "      <td id=\"T_42a22_row36_col0\" class=\"data row36 col0\" >1755</td>\n", "      <td id=\"T_42a22_row36_col1\" class=\"data row36 col1\" >18</td>\n", "      <td id=\"T_42a22_row36_col2\" class=\"data row36 col2\" >'3'</td>\n", "      <td id=\"T_42a22_row36_col3\" class=\"data row36 col3\" >18</td>\n", "      <td id=\"T_42a22_row36_col4\" class=\"data row36 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row36_col5\" class=\"data row36 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row37\" class=\"row_heading level0 row37\" >37</th>\n", "      <td id=\"T_42a22_row37_col0\" class=\"data row37 col0\" >1756</td>\n", "      <td id=\"T_42a22_row37_col1\" class=\"data row37 col1\" >19</td>\n", "      <td id=\"T_42a22_row37_col2\" class=\"data row37 col2\" >'4'</td>\n", "      <td id=\"T_42a22_row37_col3\" class=\"data row37 col3\" >19</td>\n", "      <td id=\"T_42a22_row37_col4\" class=\"data row37 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row37_col5\" class=\"data row37 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row38\" class=\"row_heading level0 row38\" >38</th>\n", "      <td id=\"T_42a22_row38_col0\" class=\"data row38 col0\" >1757</td>\n", "      <td id=\"T_42a22_row38_col1\" class=\"data row38 col1\" >13146</td>\n", "      <td id=\"T_42a22_row38_col2\" class=\"data row38 col2\" >'다'</td>\n", "      <td id=\"T_42a22_row38_col3\" class=\"data row38 col3\" >13146</td>\n", "      <td id=\"T_42a22_row38_col4\" class=\"data row38 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row38_col5\" class=\"data row38 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row39\" class=\"row_heading level0 row39\" >39</th>\n", "      <td id=\"T_42a22_row39_col0\" class=\"data row39 col0\" >1758</td>\n", "      <td id=\"T_42a22_row39_col1\" class=\"data row39 col1\" >382</td>\n", "      <td id=\"T_42a22_row39_col2\" class=\"data row39 col2\" >'.\\n\\n'</td>\n", "      <td id=\"T_42a22_row39_col3\" class=\"data row39 col3\" >382</td>\n", "      <td id=\"T_42a22_row39_col4\" class=\"data row39 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row39_col5\" class=\"data row39 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row40\" class=\"row_heading level0 row40\" >40</th>\n", "      <td id=\"T_42a22_row40_col0\" class=\"data row40 col0\" >1759</td>\n", "      <td id=\"T_42a22_row40_col1\" class=\"data row40 col1\" >59</td>\n", "      <td id=\"T_42a22_row40_col2\" class=\"data row40 col2\" >'\\\\'</td>\n", "      <td id=\"T_42a22_row40_col3\" class=\"data row40 col3\" >59</td>\n", "      <td id=\"T_42a22_row40_col4\" class=\"data row40 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row40_col5\" class=\"data row40 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row41\" class=\"row_heading level0 row41\" >41</th>\n", "      <td id=\"T_42a22_row41_col0\" class=\"data row41 col0\" >1760</td>\n", "      <td id=\"T_42a22_row41_col1\" class=\"data row41 col1\" >79075</td>\n", "      <td id=\"T_42a22_row41_col2\" class=\"data row41 col2\" >'boxed'</td>\n", "      <td id=\"T_42a22_row41_col3\" class=\"data row41 col3\" >79075</td>\n", "      <td id=\"T_42a22_row41_col4\" class=\"data row41 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row41_col5\" class=\"data row41 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row42\" class=\"row_heading level0 row42\" >42</th>\n", "      <td id=\"T_42a22_row42_col0\" class=\"data row42 col0\" >1761</td>\n", "      <td id=\"T_42a22_row42_col1\" class=\"data row42 col1\" >90</td>\n", "      <td id=\"T_42a22_row42_col2\" class=\"data row42 col2\" >'{'</td>\n", "      <td id=\"T_42a22_row42_col3\" class=\"data row42 col3\" >90</td>\n", "      <td id=\"T_42a22_row42_col4\" class=\"data row42 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row42_col5\" class=\"data row42 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row43\" class=\"row_heading level0 row43\" >43</th>\n", "      <td id=\"T_42a22_row43_col0\" class=\"data row43 col0\" >1762</td>\n", "      <td id=\"T_42a22_row43_col1\" class=\"data row43 col1\" >21</td>\n", "      <td id=\"T_42a22_row43_col2\" class=\"data row43 col2\" >'6'</td>\n", "      <td id=\"T_42a22_row43_col3\" class=\"data row43 col3\" >21</td>\n", "      <td id=\"T_42a22_row43_col4\" class=\"data row43 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row43_col5\" class=\"data row43 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row44\" class=\"row_heading level0 row44\" >44</th>\n", "      <td id=\"T_42a22_row44_col0\" class=\"data row44 col0\" >1763</td>\n", "      <td id=\"T_42a22_row44_col1\" class=\"data row44 col1\" >18</td>\n", "      <td id=\"T_42a22_row44_col2\" class=\"data row44 col2\" >'3'</td>\n", "      <td id=\"T_42a22_row44_col3\" class=\"data row44 col3\" >18</td>\n", "      <td id=\"T_42a22_row44_col4\" class=\"data row44 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row44_col5\" class=\"data row44 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row45\" class=\"row_heading level0 row45\" >45</th>\n", "      <td id=\"T_42a22_row45_col0\" class=\"data row45 col0\" >1764</td>\n", "      <td id=\"T_42a22_row45_col1\" class=\"data row45 col1\" >19</td>\n", "      <td id=\"T_42a22_row45_col2\" class=\"data row45 col2\" >'4'</td>\n", "      <td id=\"T_42a22_row45_col3\" class=\"data row45 col3\" >19</td>\n", "      <td id=\"T_42a22_row45_col4\" class=\"data row45 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row45_col5\" class=\"data row45 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row46\" class=\"row_heading level0 row46\" >46</th>\n", "      <td id=\"T_42a22_row46_col0\" class=\"data row46 col0\" >1765</td>\n", "      <td id=\"T_42a22_row46_col1\" class=\"data row46 col1\" >92</td>\n", "      <td id=\"T_42a22_row46_col2\" class=\"data row46 col2\" >'}'</td>\n", "      <td id=\"T_42a22_row46_col3\" class=\"data row46 col3\" >92</td>\n", "      <td id=\"T_42a22_row46_col4\" class=\"data row46 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row46_col5\" class=\"data row46 col5\" >✅</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row47\" class=\"row_heading level0 row47\" >47</th>\n", "      <td id=\"T_42a22_row47_col0\" class=\"data row47 col0\" >1766</td>\n", "      <td id=\"T_42a22_row47_col1\" class=\"data row47 col1\" >151645</td>\n", "      <td id=\"T_42a22_row47_col2\" class=\"data row47 col2\" >'<|im_end|>' 🔚 EOS</td>\n", "      <td id=\"T_42a22_row47_col3\" class=\"data row47 col3\" >-100</td>\n", "      <td id=\"T_42a22_row47_col4\" class=\"data row47 col4\" >MASKED</td>\n", "      <td id=\"T_42a22_row47_col5\" class=\"data row47 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row48\" class=\"row_heading level0 row48\" >48</th>\n", "      <td id=\"T_42a22_row48_col0\" class=\"data row48 col0\" >1767</td>\n", "      <td id=\"T_42a22_row48_col1\" class=\"data row48 col1\" >198</td>\n", "      <td id=\"T_42a22_row48_col2\" class=\"data row48 col2\" >'\\n'</td>\n", "      <td id=\"T_42a22_row48_col3\" class=\"data row48 col3\" >-100</td>\n", "      <td id=\"T_42a22_row48_col4\" class=\"data row48 col4\" >MASKED</td>\n", "      <td id=\"T_42a22_row48_col5\" class=\"data row48 col5\" >❌</td>\n", "    </tr>\n", "    <tr>\n", "      <th id=\"T_42a22_level0_row49\" class=\"row_heading level0 row49\" >49</th>\n", "      <td id=\"T_42a22_row49_col0\" class=\"data row49 col0\" >1768</td>\n", "      <td id=\"T_42a22_row49_col1\" class=\"data row49 col1\" >151645</td>\n", "      <td id=\"T_42a22_row49_col2\" class=\"data row49 col2\" >'<|im_end|>' 🔚 EOS</td>\n", "      <td id=\"T_42a22_row49_col3\" class=\"data row49 col3\" >151645</td>\n", "      <td id=\"T_42a22_row49_col4\" class=\"data row49 col4\" >TRAINED</td>\n", "      <td id=\"T_42a22_row49_col5\" class=\"data row49 col5\" >✅</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n"], "text/plain": ["<pandas.io.formats.style.Styler at 0x7f4578ddae00>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "🔍 EOS TOKEN ANALYSIS:\n", "--------------------------------------------------\n", "✅ Sequence ENDS with EOS token\n", "   Last token: 151645 = '<|im_end|>'\n", "   Label: 151645 (TRAINED)\n", "\n", "🎯 EOS tokens found at positions: [19, 74, 1766, 1768]\n", "   Position 19: label = -100 (MASKED)\n", "   Position 74: label = -100 (MASKED)\n", "   Position 1766: label = -100 (MASKED)\n", "   Position 1768: label = 151645 (TRAINED)\n"]}], "source": ["def show_last_tokens_analysis(sample_data, num_tokens=50):\n", "    \"\"\"Show the last N tokens to check for EOS token and sequence ending.\"\"\"\n", "    \n", "    input_ids = sample_data['input_ids']\n", "    labels = sample_data['labels']\n", "    total_tokens = len(input_ids)\n", "    \n", "    print(f\"\\n🔚 LAST {num_tokens} TOKENS ANALYSIS (to check for EOS token)\")\n", "    print(\"=\" * 100)\n", "    print(f\"Total tokens in sequence: {total_tokens}\")\n", "    print(f\"Showing tokens {max(0, total_tokens-num_tokens)} to {total_tokens-1}\")\n", "    print(f\"EOS token ID: {tokenizer.eos_token_id} ('{tokenizer.eos_token}')\")\n", "    print(\"=\" * 100)\n", "    \n", "    # Create table for last tokens\n", "    last_token_rows = []\n", "    start_idx = max(0, total_tokens - num_tokens)\n", "    \n", "    for i in range(start_idx, total_tokens):\n", "        token_id = input_ids[i]\n", "        label = labels[i]\n", "        token_text = tokenizer.decode([token_id])\n", "        \n", "        # Check if this is EOS token\n", "        is_eos = token_id == tokenizer.eos_token_id\n", "        eos_marker = \" 🔚 EOS\" if is_eos else \"\"\n", "        \n", "        # Determine status\n", "        if label == -100:\n", "            status = \"MASKED\"\n", "            label_display = \"-100\"\n", "        else:\n", "            status = \"TRAINED\"\n", "            label_display = str(label)\n", "        \n", "        last_token_rows.append({\n", "            'Position': i,\n", "            'Input Token ID': token_id,\n", "            'Decoded Text': repr(token_text) + eos_marker,\n", "            'Label': label_display,\n", "            'Status': status,\n", "            'Contributes to Loss': \"❌\" if label == -100 else \"✅\"\n", "        })\n", "    \n", "    last_df = pd.DataFrame(last_token_rows)\n", "    \n", "    # Style the dataframe\n", "    def highlight_status(val):\n", "        if val == 'MASKED':\n", "            return 'background-color: #ffcccc; color: #cc0000; font-weight: bold'\n", "        elif val == 'TRAINED':\n", "            return 'background-color: #ccffcc; color: #006600; font-weight: bold'\n", "        return ''\n", "    \n", "    def highlight_label(val):\n", "        if val == '-100':\n", "            return 'background-color: #ffcccc; color: #cc0000; font-weight: bold'\n", "        return 'background-color: #ccffcc; color: #006600'\n", "    \n", "    def highlight_eos(val):\n", "        if '🔚 EOS' in str(val):\n", "            return 'background-color: #ffffcc; color: #cc6600; font-weight: bold; border: 2px solid #cc6600'\n", "        return ''\n", "    \n", "    styled_last_df = last_df.style.applymap(highlight_status, subset=['Status']) \\\n", "                                  .applymap(highlight_label, subset=['Label']) \\\n", "                                  .applymap(highlight_eos, subset=['Decoded Text'])\n", "    \n", "    display(styled_last_df)\n", "    \n", "    # Check if sequence ends with EOS\n", "    print(\"\\n🔍 EOS TOKEN ANALYSIS:\")\n", "    print(\"-\" * 50)\n", "    \n", "    if total_tokens > 0 and input_ids[-1] == tokenizer.eos_token_id:\n", "        print(\"✅ Sequence ENDS with EOS token\")\n", "        print(f\"   Last token: {input_ids[-1]} = '{tokenizer.decode([input_ids[-1]])}'\")\n", "        print(f\"   Label: {labels[-1]} ({'MASKED' if labels[-1] == -100 else 'TRAINED'})\")\n", "    else:\n", "        print(\"⚠️  Sequence does NOT end with EOS token\")\n", "        if total_tokens > 0:\n", "            print(f\"   Last token: {input_ids[-1]} = '{tokenizer.decode([input_ids[-1]])}'\")\n", "            print(f\"   Label: {labels[-1]} ({'MASKED' if labels[-1] == -100 else 'TRAINED'})\")\n", "    \n", "    # Check for any EOS tokens in the sequence\n", "    eos_positions = [i for i, token_id in enumerate(input_ids) if token_id == tokenizer.eos_token_id]\n", "    if eos_positions:\n", "        print(f\"\\n🎯 EOS tokens found at positions: {eos_positions}\")\n", "        for pos in eos_positions:\n", "            label_status = 'MASKED' if labels[pos] == -100 else 'TRAINED'\n", "            print(f\"   Position {pos}: label = {labels[pos]} ({label_status})\")\n", "    else:\n", "        print(f\"\\n❌ No EOS tokens found in the entire sequence\")\n", "    \n", "    return last_df\n", "\n", "# Show last 50 tokens analysis\n", "last_tokens_df = show_last_tokens_analysis(sample_data, num_tokens=50)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Compare Multiple Samples"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📊 INSTRUCTION TUNING ANALYSIS ACROSS ALL SAMPLES\n", "======================================================================\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th><PERSON><PERSON></th>\n", "      <th>Total Tokens</th>\n", "      <th>Masked (-100)</th>\n", "      <th>Trained (token_id)</th>\n", "      <th>Training Ratio</th>\n", "      <th>User Chars</th>\n", "      <th>Assistant Chars</th>\n", "      <th>Question Preview</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>1769</td>\n", "      <td>81</td>\n", "      <td>1688</td>\n", "      <td>95.4%</td>\n", "      <td>161</td>\n", "      <td>2651</td>\n", "      <td>Let $\\frac{m}{n}$ , in lowest terms, be ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>2048</td>\n", "      <td>100</td>\n", "      <td>1948</td>\n", "      <td>95.1%</td>\n", "      <td>224</td>\n", "      <td>16129</td>\n", "      <td>The shortest distances between an interi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>2048</td>\n", "      <td>140</td>\n", "      <td>1908</td>\n", "      <td>93.2%</td>\n", "      <td>374</td>\n", "      <td>20975</td>\n", "      <td>Three concentric circles have radii $3,$...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Sample  Total Tokens  Masked (-100)  Trained (token_id) Training Ratio  \\\n", "0       1          1769             81                1688          95.4%   \n", "1       2          2048            100                1948          95.1%   \n", "2       3          2048            140                1908          93.2%   \n", "\n", "   User Chars  Assistant Chars                             Question Preview  \n", "0         161             2651  Let $\\frac{m}{n}$ , in lowest terms, be ...  \n", "1         224            16129  The shortest distances between an interi...  \n", "2         374            20975  Three concentric circles have radii $3,$...  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "📈 SUMMARY STATISTICS:\n", "Average training ratio: 94.6%\n", "Average total tokens: 1955\n", "Average masked tokens: 107\n", "Average trained tokens: 1848\n"]}], "source": ["def analyze_all_samples():\n", "    \"\"\"Analyze masking patterns across all samples.\"\"\"\n", "    \n", "    results = []\n", "    \n", "    for i, sample in enumerate(formatted_samples):\n", "        messages = sample[\"messages\"]\n", "        \n", "        # Get instruction tuning data\n", "        input_ids, labels = instruction_pipeline._tokenize_with_masking(messages, tokenizer)\n", "        \n", "        # Calculate statistics\n", "        total_tokens = len(input_ids)\n", "        masked_tokens = sum(1 for l in labels if l == -100)\n", "        trained_tokens = total_tokens - masked_tokens\n", "        training_ratio = trained_tokens / total_tokens * 100\n", "        \n", "        # Get conversation info\n", "        user_content = next((msg['content'] for msg in messages if msg['role'] == 'user'), \"\")\n", "        assistant_content = next((msg['content'] for msg in messages if msg['role'] == 'assistant'), \"\")\n", "        \n", "        results.append({\n", "            'Sample': i + 1,\n", "            'Total Tokens': total_tokens,\n", "            'Masked (-100)': masked_tokens,\n", "            'Trained (token_id)': trained_tokens,\n", "            'Training Ratio': f\"{training_ratio:.1f}%\",\n", "            'User Chars': len(user_content),\n", "            'Assistant Chars': len(assistant_content),\n", "            'Question Preview': user_content[:40] + \"...\" if len(user_content) > 40 else user_content\n", "        })\n", "    \n", "    df = pd.DataFrame(results)\n", "    \n", "    print(\"\\n📊 INSTRUCTION TUNING ANALYSIS ACROSS ALL SAMPLES\")\n", "    print(\"=\" * 70)\n", "    display(df)\n", "    \n", "    # Summary statistics\n", "    avg_training_ratio = sum(float(r['Training Ratio'].rstrip('%')) for r in results) / len(results)\n", "    print(f\"\\n📈 SUMMARY STATISTICS:\")\n", "    print(f\"Average training ratio: {avg_training_ratio:.1f}%\")\n", "    print(f\"Average total tokens: {df['Total Tokens'].mean():.0f}\")\n", "    print(f\"Average masked tokens: {df['Masked (-100)'].mean():.0f}\")\n", "    print(f\"Average trained tokens: {df['Trained (token_id)'].mean():.0f}\")\n", "    \n", "    return df\n", "\n", "# Analyze all samples\n", "analysis_df = analyze_all_samples()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.0"}}, "nbformat": 4, "nbformat_minor": 4}