#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to process the JSONL file to:
1. Remove the "answer" column
2. Remove numbering from the solution
3. Ensure the solution ends with a boxed answer format
"""

import json
import re
import sys
from typing import Dict, Any

def extract_final_answer(solution: str) -> str:
    """Extract the final numerical answer from the solution."""
    # Look for boxed answers first
    boxed_matches = re.findall(r'\\boxed\{([^}]+)\}', solution)
    if boxed_matches:
        return boxed_matches[-1]  # Take the last boxed answer
    
    # Look for "Final Answer" sections
    final_answer_matches = re.findall(r'\*\*Final Answer\*\*[^\n]*\n.*?(\d+)', solution, re.IGNORECASE)
    if final_answer_matches:
        return final_answer_matches[-1]
    
    # Look for standalone numbers at the end
    lines = solution.strip().split('\n')
    for line in reversed(lines):
        line = line.strip()
        if line and re.match(r'^\d+\.?$', line):
            return line.rstrip('.')
    
    # Look for "답은" (Korean for "answer is")
    answer_matches = re.findall(r'답은\s*(\d+)', solution)
    if answer_matches:
        return answer_matches[-1]
    
    # Look for "answer is" or "답:"
    answer_matches = re.findall(r'(?:answer is|답:)\s*(\d+)', solution, re.IGNORECASE)
    if answer_matches:
        return answer_matches[-1]
    
    # Default fallback - look for any number pattern
    number_matches = re.findall(r'\b(\d+)\b', solution)
    if number_matches:
        return number_matches[-1]
    
    return "0"  # Fallback

def remove_numbering(text: str) -> str:
    """Remove numbered list formatting from the solution."""
    lines = text.split('\n')
    cleaned_lines = []
    
    for line in lines:
        # Remove leading numbers followed by period and space
        cleaned_line = re.sub(r'^\d+\.\s*', '', line)
        cleaned_lines.append(cleaned_line)
    
    return '\n'.join(cleaned_lines)

def ensure_boxed_answer(solution: str) -> str:
    """Ensure the solution ends with a properly formatted boxed answer."""
    # Extract the final answer
    final_answer = extract_final_answer(solution)
    
    # Remove existing boxed answers and final answer sections
    cleaned_solution = re.sub(r'\\boxed\{[^}]+\}', '', solution)
    cleaned_solution = re.sub(r'\*\*Final Answer\*\*.*$', '', cleaned_solution, flags=re.MULTILINE | re.DOTALL)
    
    # Remove trailing empty lines and filler content
    lines = cleaned_solution.split('\n')
    cleaned_lines = []
    
    for line in lines:
        line = line.strip()
        # Skip empty lines, filler content, and standalone numbers at the end
        if (line and 
            not re.match(r'^\d+\.?$', line) and
            '이하 문장 수 맞추기' not in line and
            'filler' not in line.lower() and
            not re.match(r'^\s*$', line)):
            cleaned_lines.append(line)
    
    # Remove trailing empty elements
    while cleaned_lines and not cleaned_lines[-1].strip():
        cleaned_lines.pop()
    
    # Join back and add the boxed answer
    cleaned_solution = '\n'.join(cleaned_lines)
    
    # Add the final boxed answer
    if cleaned_solution and not cleaned_solution.endswith('\n'):
        cleaned_solution += '\n'
    
    cleaned_solution += f'\n\\boxed{{{final_answer}}}'
    
    return cleaned_solution

def process_entry(entry: Dict[str, Any]) -> Dict[str, Any]:
    """Process a single JSONL entry."""
    # Remove the answer field
    processed_entry = {
        "question": entry["question"],
        "solution": entry["solution"]
    }
    
    # Remove numbering from solution
    processed_entry["solution"] = remove_numbering(processed_entry["solution"])
    
    # Ensure boxed answer format
    processed_entry["solution"] = ensure_boxed_answer(processed_entry["solution"])
    
    return processed_entry

def main():
    """Main function to process the JSONL file."""
    input_file = "data/code_switched_GAIR_LIMO_train_817.jsonl"
    output_file = "data/processed_code_switched_GAIR_LIMO_train_817.jsonl"
    
    print(f"Processing {input_file}...")
    
    processed_count = 0
    
    try:
        with open(input_file, 'r', encoding='utf-8') as infile, \
             open(output_file, 'w', encoding='utf-8') as outfile:
            
            for line_num, line in enumerate(infile, 1):
                try:
                    # Parse the JSON line
                    entry = json.loads(line.strip())
                    
                    # Process the entry
                    processed_entry = process_entry(entry)
                    
                    # Write the processed entry
                    outfile.write(json.dumps(processed_entry, ensure_ascii=False) + '\n')
                    
                    processed_count += 1
                    
                    if processed_count % 100 == 0:
                        print(f"Processed {processed_count} entries...")
                        
                except json.JSONDecodeError as e:
                    print(f"Error parsing line {line_num}: {e}")
                    continue
                except Exception as e:
                    print(f"Error processing line {line_num}: {e}")
                    continue
        
        print(f"✅ Successfully processed {processed_count} entries")
        print(f"📁 Output saved to: {output_file}")
        
        # Show a sample of the processed data
        print("\n📋 Sample of processed data:")
        with open(output_file, 'r', encoding='utf-8') as f:
            sample = json.loads(f.readline())
            print(f"Question: {sample['question'][:100]}...")
            print(f"Solution preview: {sample['solution'][:200]}...")
            print(f"Solution ends with: ...{sample['solution'][-50:]}")
            
    except FileNotFoundError:
        print(f"❌ Error: Input file {input_file} not found")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
